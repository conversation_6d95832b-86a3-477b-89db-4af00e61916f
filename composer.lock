{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "b48d6a265f9c38f55e2c328d1b1e7946", "packages": [{"name": "ajbcn/aglaia", "version": "10.0.24", "source": {"type": "git", "url": "*********************:drupal/profiles/aglaia.git", "reference": "72bf3c598d953744f2d64ca57a3b8844271ea62d"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/aglaia/ajbcn-aglaia-10.0.24-854297.zip", "reference": "72bf3c598d953744f2d64ca57a3b8844271ea62d", "shasum": "a195c81d1a6059700e786858c9839ec01bc1dfcf"}, "require": {"ajbcn/aglaia_menu": "^10.0", "ajbcn/aglaia_overrides": "^10.0", "ajbcn/aglaia_theme": "^10.0", "ajbcn/ajuntament_accessibilitat": "^10.0", "ajbcn/ajuntament_avislegal": "^10.0", "ajbcn/ajuntament_avisos": "^10.0", "ajbcn/ajuntament_banners": "^10.0", "ajbcn/ajuntament_embed_video": "^10.0", "ajbcn/ajuntament_infofile": "^10.0", "ajbcn/ajuntament_media_link": "^10.0", "ajbcn/ajuntament_social_share": "^10.0", "ajbcn/ajuntament_weight": "^10.0", "ajbcn/bcn_cms_stats": "^9.0", "ajbcn/bcn_redirect_home": "^9.1", "bower-asset/fancybox": "^3.0", "cweagans/composer-patches": "^1.6.5", "drupal/admin_toolbar": "^3.0", "drupal/allowed_formats": "^3.0", "drupal/block_content_permissions": "^1.10", "drupal/color_field": "^3.0", "drupal/colorbox": "^2.0", "drupal/crop": "^2.1", "drupal/draggableviews": "^2.1", "drupal/ds": "^3.12", "drupal/editor_advanced_link": "^2.1", "drupal/entity_browser": "^2.3", "drupal/entity_clone": "^1.0@beta", "drupal/field_group": "^3.1", "drupal/file_browser": "^1.3", "drupal/file_replace": "^1.3", "drupal/flippy": "^2.0@beta", "drupal/fontawesome": "^2.18", "drupal/image_widget_crop": "^2.3", "drupal/inline_entity_form": "^1.0@RC", "drupal/link_attributes": "^2.0", "drupal/menu_admin_per_menu": "^1.1", "drupal/menu_breadcrumb": "^2.0@alpha", "drupal/menu_multilingual": "^1.0@alpha", "drupal/metatag": "^2.0", "drupal/paragraphs": "^1.12", "drupal/paragraphs_asymmetric_translation_widgets": "^1.2", "drupal/pathauto": "^1.7", "drupal/redirect": "^1.8", "drupal/simple_sitemap": "^4.1", "drupal/sitemap": "^2.0@beta", "drupal/svg_image": "^3.0", "drupal/svg_image_field": "^2.2", "drupal/tvi": "^2.0.x-dev@dev", "drupal/twig_tweak": "^3.2", "drupal/twig_vardumper": "^3", "drupal/unpublished_node_permissions": "^1.2", "drupal/views_bulk_operations": "^4", "drupal/views_infinite_scroll": "^2.0", "npm-asset/accessible360--accessible-slick": "1.0.1", "npm-asset/blazy": "^1.8", "npm-asset/cropper": "^4.0", "scssphp/scssphp": "^1.4", "webmozart/path-util": "^2.3"}, "type": "ajbcn-drupal-profile", "extra": {"composer-exit-on-patch-failure": false, "patches": {"drupal/paragraphs_asymmetric_translation_widgets": {"2959900-13: Add support for experimental widget": "https://www.drupal.org/files/issues/2018-11-12/2959900-13.patch"}, "drupal/paragraphs": {"3161823-4: Paragraphs widget not constrained to container width in Claro": "https://www.drupal.org/files/issues/2020-07-29/3161823-4-set-form-element-width.patch"}, "drupal/core": {"3062863-3: Remove duplicate suggestions": "https://www.drupal.org/files/issues/2019-06-19/3062863-3.patch", "3361177-1: An empty views pager offset field can cause a PHP type error to be triggered.": "https://www.drupal.org/files/issues/2023-05-25/3361177-1.patch", "[PP-1] Use hook_theme_suggestions in views": "https://drupal.org/files/issues/2023-06-12/2923634-92.patch", "2868294-87: Call to a member function getThirdPartySetting() on null in ContentTranslationManager": "https://www.drupal.org/files/issues/2022-11-30/2868294-87.patch", "3145188_46: Paragraphs fields cause overflow of content forms in Claro": "https://www.drupal.org/files/issues/2023-12-06/3145188_46.patch", "3400204: The Claro admin theme's layout breaks when you try to add a long string to the CKEditor5 window.": "https://www.drupal.org/files/issues/2024-06-27/3400204-claro-layout-overflow-fix-2.patch"}, "drupal/ds": {"2895316-12: Parent theme template inheritance bug": "https://www.drupal.org/files/issues/2021-02-22/2895316-12.patch", "3331674-22: Notice: Undefined index: #entity_type in ds_preprocess_ds_layout()": "https://www.drupal.org/files/issues/2025-07-09/ds-undefined_array_key-3331674-22_0.patch"}, "drupal/flippy": {"1497070-15: Allow Flippy to filter content by taxonomy": "https://www.drupal.org/files/issues/2020-01-22/flippy-filter-taxonomy-1497070-15.patch"}, "drupal/block_content_permissions": {"2920739-38: Allow accessing the 'Custom block library' page without 'Administer blocks' permission": "https://www.drupal.org/files/issues/2020-07-27/block_content_permissions-access_listing_page-2920739-38.patch"}}, "enable-patching": "true"}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "description": "Distribucio Aglaia de l'Ajuntament de Barcelona", "time": "2025-07-10T09:57:16+00:00"}, {"name": "ajbcn/aglaia_menu", "version": "10.0.6", "source": {"type": "git", "url": "*********************:drupal/modules/aglaia-menu.git", "reference": "91e6650f616c40f71c3d023b0f57db0f3ca10c26"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/aglaia_menu/ajbcn-aglaia_menu-10.0.6-e4e301.zip", "reference": "91e6650f616c40f71c3d023b0f57db0f3ca10c26", "shasum": "5231a0cac9a00a22d2690f5cacead057fc6e1ba7"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "description": "Mòdul de menú de la distribució Aglaia de l'Ajuntament de Barcelona", "time": "2024-07-10T15:19:28+00:00"}, {"name": "ajbcn/aglaia_overrides", "version": "10.0.4", "source": {"type": "git", "url": "*********************:drupal/modules/aglaia-overrides.git", "reference": "2f01ee4a491d5ba09dc27afb3df0157ea424212c"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/aglaia_overrides/ajbcn-aglaia_overrides-10.0.4-f3a5f4.zip", "reference": "2f01ee4a491d5ba09dc27afb3df0157ea424212c", "shasum": "ff5e824d6c1100345c34751d89522249cd1b73b8"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "time": "2024-11-28T10:30:01+00:00"}, {"name": "ajbcn/aglaia_theme", "version": "10.0.7", "source": {"type": "git", "url": "*********************:drupal/themes/aglaia-theme.git", "reference": "44099528006470145f15bef0bd84a1b9afb5f9b2"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/aglaia_theme/ajbcn-aglaia_theme-10.0.7-44b264.zip", "reference": "44099528006470145f15bef0bd84a1b9afb5f9b2", "shasum": "75569fbb19d1409d3a9b698e091a0a38db68af26"}, "type": "ajbcn-drupal-theme", "description": "Tema Aglaia de l'Ajuntament de Barcelona", "time": "2025-04-14T06:40:55+00:00"}, {"name": "ajbcn/ajuntament_accessibilitat", "version": "10.0.0", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-accessibilitat.git", "reference": "24ca8cd857f2fcbabe3789a1df1bd0fde528ae55"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_accessibilitat/ajbcn-ajuntament_accessibilitat-10.0.0-a95fee.zip", "reference": "24ca8cd857f2fcbabe3789a1df1bd0fde528ae55", "shasum": "792433f5f7223b7defbedfd9f88c669f03cf893a"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "description": "MÒDUL OBSOLET: Mòdul d'accessibilitat de l'Ajuntament de Barcelona", "time": "2023-05-31T15:44:40+00:00"}, {"name": "ajbcn/ajuntament_avislegal", "version": "10.0.4", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-avislegal.git", "reference": "36afa7b190baca0fa16b54d760bdbde3a755459a"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_avislegal/ajbcn-ajuntament_avislegal-10.0.4-52fcc6.zip", "reference": "36afa7b190baca0fa16b54d760bdbde3a755459a", "shasum": "50ebb43929ef40be18197bfa935dc3aebb3c2e17"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "description": "Mòdul d'avis legal de l'Ajuntament de Barcelona", "time": "2024-02-01T12:00:47+00:00"}, {"name": "ajbcn/ajuntament_avisos", "version": "10.0.1", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-avisos.git", "reference": "cb1561bc76bbf103c41d67cadada07c7f5a8de50"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_avisos/ajbcn-ajuntament_avisos-10.0.1-f0aaaa.zip", "reference": "cb1561bc76bbf103c41d67cadada07c7f5a8de50", "shasum": "a457f9bf86989f31017eb1bc358e0aff890f19ec"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "description": "Mòdul d'avisos de l'Ajuntament de Barcelona", "time": "2023-09-08T07:10:31+00:00"}, {"name": "ajbcn/ajuntament_banners", "version": "10.0.8", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-banners.git", "reference": "394fa2e03e7eeb0c1bcc1bc5a4015b0c79e27013"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_banners/ajbcn-ajuntament_banners-10.0.8-0f71fd.zip", "reference": "394fa2e03e7eeb0c1bcc1bc5a4015b0c79e27013", "shasum": "e061bfac0e80b48a92d1c0257a789d3fa1b93973"}, "require": {"ajbcn/ajuntament_embed_video": "^10.0", "ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0", "drupal/allowed_formats": "^3.0", "drupal/crop": "^2.1", "drupal/entity_browser": "^2.6", "drupal/image_widget_crop": "^2.3", "drupal/inline_entity_form": "^1.0@RC", "drupal/link_attributes": "^2.1", "drupal/paragraphs": "^1.12", "drupal/paragraphs_asymmetric_translation_widgets": "^1.2"}, "type": "ajbcn-drupal-module", "description": "Crea els elements necessaris per mostrar i administrar un carrousel de banners.", "time": "2025-04-29T09:11:40+00:00"}, {"name": "ajbcn/ajuntament_embed_video", "version": "10.0.4", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-embed-video.git", "reference": "92a6c4537eb0cbc8245548ef730fb92650205a54"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_embed_video/ajbcn-ajuntament_embed_video-10.0.4-7a2143.zip", "reference": "92a6c4537eb0cbc8245548ef730fb92650205a54", "shasum": "9708799cebe2409407b04b214d9ca30ebfa2ce2e"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "description": "Mòdul per embbedar videos de l'Ajuntament de Barcelona", "time": "2025-04-25T15:46:09+00:00"}, {"name": "ajbcn/ajuntament_infofile", "version": "10.0.1", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-infofile.git", "reference": "ba743f5eb58c0e839c4d96d62b098384a439a3f3"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_infofile/ajbcn-ajuntament_infofile-10.0.1-2c0661.zip", "reference": "ba743f5eb58c0e839c4d96d62b098384a439a3f3", "shasum": "3dc92ef6d1f963de2fb5ae765cd060fa1c84f90a"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "time": "2023-09-08T07:31:45+00:00"}, {"name": "ajbcn/ajuntament_media_link", "version": "10.0.2", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-media-link.git", "reference": "1c235ac81ee2bc5dafa8c6e306259f0d4cb7634f"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_media_link/ajbcn-ajuntament_media_link-10.0.2-1531dd.zip", "reference": "1c235ac81ee2bc5dafa8c6e306259f0d4cb7634f", "shasum": "8c63dcafbd97e636d68a0600c303dd83d43bcb73"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "description": "Mòdul per crear enllaços externs a media de l'Ajuntament de Barcelona", "time": "2024-06-28T10:24:34+00:00"}, {"name": "ajbcn/ajuntament_social_share", "version": "10.0.5", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-social-share.git", "reference": "6048a96753031077c6d3bfcc7f8edec169e532a1"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_social_share/ajbcn-ajuntament_social_share-10.0.5-ba89ed.zip", "reference": "6048a96753031077c6d3bfcc7f8edec169e532a1", "shasum": "966755e6e1d2d6bd92ba93a7ea44b81bc5e63be3"}, "require": {"ajbcn/bcn_cms_stats": "^9.0", "composer/installers": "^2.0"}, "type": "ajbcn-drupal-module", "authors": [{"name": "Producciobcn", "email": "<EMAIL>"}], "time": "2025-02-03T11:00:42+00:00"}, {"name": "ajbcn/ajuntament_weight", "version": "10.0.1", "source": {"type": "git", "url": "*********************:drupal/modules/ajuntament-weight.git", "reference": "d6e21e7d3f5087a4a71ea9f9f687c6621f14f376"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/ajuntament_weight/ajbcn-ajuntament_weight-10.0.1-5ee504.zip", "reference": "d6e21e7d3f5087a4a71ea9f9f687c6621f14f376", "shasum": "f051813af8812485c55a63d8927a4c89f5de333e"}, "require": {"ajbcn/bcn_cms_stats": "^9.0"}, "type": "ajbcn-drupal-module", "description": "Permet ordenació de continguts", "time": "2023-09-08T08:43:51+00:00"}, {"name": "ajbcn/bcn_barra", "version": "9.0.7", "source": {"type": "git", "url": "*********************:drupal/modules/bcn-barra.git", "reference": "13ab787b2d19b95da223427992403c839636f7a1"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/bcn_barra/ajbcn-bcn_barra-9.0.7-636b12.zip", "reference": "13ab787b2d19b95da223427992403c839636f7a1", "shasum": "3bd8c64a06e781638acb312fa90cca6cce1417e7"}, "require": {"ajbcn/bcn_cms_stats": "*"}, "type": "ajbcn-drupal-module", "archive": {"exclude": [".editorconfig", ".giti<PERSON>re", ".DEVS.md"]}, "authors": [{"name": "Canals digitals", "email": "<EMAIL>"}], "description": "Mòdul que afageix la barra corporativa oficial de l'Ajuntament de Barcelona", "time": "2025-05-20T08:35:52+00:00"}, {"name": "ajbcn/bcn_cms_stats", "version": "9.0.4", "source": {"type": "git", "url": "*********************:drupal/modules/bcn-cms-stats.git", "reference": "f73638753737792c0c8b146ee52e8245fcf22e7b"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/bcn_cms_stats/ajbcn-bcn_cms_stats-9.0.4-dc36f2.zip", "reference": "f73638753737792c0c8b146ee52e8245fcf22e7b", "shasum": "006377ba8a97314a78492458405342efb114cd2b"}, "type": "ajbcn-drupal-module", "archive": {"exclude": [".editorconfig", ".giti<PERSON>re", ".DEVS.md"]}, "authors": [{"name": "Canals digitals", "email": "<EMAIL>"}], "description": "BCN CMS Stats client service. Tracks BCN CMS sites statistics.", "time": "2025-05-20T08:55:17+00:00"}, {"name": "ajbcn/bcn_redirect_home", "version": "9.1.11", "source": {"type": "git", "url": "*********************:drupal/modules/bcn_redireccio_inici.git", "reference": "a683371c33bedd48e22dcba8b4d89e51942ebe82"}, "dist": {"type": "zip", "url": "https://phpackages.dtibcn.cat/dist/ajbcn/bcn_redirect_home/ajbcn-bcn_redirect_home-9.1.11-42c97d.zip", "reference": "a683371c33bedd48e22dcba8b4d89e51942ebe82", "shasum": "509c81d9c19c8d3c7a1efac2746c0bf3944b7849"}, "require": {"ajbcn/bcn_cms_stats": "*"}, "type": "ajbcn-drupal-module", "archive": {"exclude": [".editorconfig", ".giti<PERSON>re", ".DEVS.md"]}, "authors": [{"name": "Canals digitals", "email": "<EMAIL>"}], "description": "Redirecciona l'usuari a la pàgina d'inici correcta.", "time": "2025-05-20T09:50:25+00:00"}, {"name": "asm89/stack-cors", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "acf3142e6c5eafa378dc8ef3c069ab4558993f70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/acf3142e6c5eafa378dc8ef3c069ab4558993f70", "reference": "acf3142e6c5eafa378dc8ef3c069ab4558993f70", "shasum": ""}, "require": {"php": "^7.3|^8.0", "symfony/http-foundation": "^5.3|^6|^7", "symfony/http-kernel": "^5.3|^6|^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "support": {"issues": "https://github.com/asm89/stack-cors/issues", "source": "https://github.com/asm89/stack-cors/tree/v2.3.0"}, "time": "2025-03-13T08:50:04+00:00"}, {"name": "bower-asset/fancybox", "version": "v3.5.7", "source": {"type": "git", "url": "https://github.com/fancyapps/fancybox.git", "reference": "b507d317a95b70a0b1be7209d528c4dba3ee0dd0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fancyapps/fancybox/zipball/b507d317a95b70a0b1be7209d528c4dba3ee0dd0", "reference": "b507d317a95b70a0b1be7209d528c4dba3ee0dd0"}, "require": {"bower-asset/jquery": ">=1.9.0"}, "type": "bower-asset", "license": ["GPL-3.0"]}, {"name": "bower-asset/jquery", "version": "3.7.1", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/fde1f76e2799dd877c176abde0ec836553246991", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "chi-teck/drupal-code-generator", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/Chi-teck/drupal-code-generator.git", "reference": "2dbd8d231945681a398862a3282ade3cf0ea23ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Chi-teck/drupal-code-generator/zipball/2dbd8d231945681a398862a3282ade3cf0ea23ab", "reference": "2dbd8d231945681a398862a3282ade3cf0ea23ab", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1.0", "psr/event-dispatcher": "^1.0", "psr/log": "^3.0", "symfony/console": "^6.3", "symfony/dependency-injection": "^6.3.2", "symfony/filesystem": "^6.3", "symfony/string": "^6.3", "twig/twig": "^3.4"}, "conflict": {"squizlabs/php_codesniffer": "<3.6"}, "require-dev": {"chi-teck/drupal-coder-extension": "^2.0.0-beta3", "drupal/coder": "8.3.23", "drupal/core": "10.3.x-dev", "ext-simplexml": "*", "phpspec/prophecy-phpunit": "^2.2", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.9", "symfony/var-dumper": "^6.4", "symfony/yaml": "^6.3", "vimeo/psalm": "^5.22.2"}, "bin": ["bin/dcg"], "type": "library", "autoload": {"psr-4": {"DrupalCodeGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal code generator", "support": {"issues": "https://github.com/Chi-teck/drupal-code-generator/issues", "source": "https://github.com/Chi-teck/drupal-code-generator/tree/3.6.1"}, "time": "2024-06-06T17:36:37+00:00"}, {"name": "composer/installers", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/12fb2dfe5e16183de69e784a7b84046c43d97e8e", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "^1.10.27 || ^2.7", "composer/semver": "^1.7.2 || ^3.4.0", "phpstan/phpstan": "^1.11", "phpstan/phpstan-phpunit": "^1", "symfony/phpunit-bridge": "^7.1.1", "symfony/process": "^5 || ^6 || ^7"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "concreteCMS", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.3.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-06-24T20:46:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "consolidation/annotated-command", "version": "4.10.1", "source": {"type": "git", "url": "https://github.com/consolidation/annotated-command.git", "reference": "362310b13ececa9f6f0a4a880811fa08fecc348b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/annotated-command/zipball/362310b13ececa9f6f0a4a880811fa08fecc348b", "reference": "362310b13ececa9f6f0a4a880811fa08fecc348b", "shasum": ""}, "require": {"consolidation/output-formatters": "^4.3.1", "php": ">=7.1.3", "psr/log": "^1 || ^2 || ^3", "symfony/console": "^4.4.8 || ^5 || ^6 || ^7", "symfony/event-dispatcher": "^4.4.8 || ^5 || ^6 || ^7", "symfony/finder": "^4.4.8 || ^5 || ^6 || ^7"}, "require-dev": {"composer-runtime-api": "^2.0", "phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\AnnotatedCommand\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Initialize Symfony Console commands from annotated command class methods.", "support": {"issues": "https://github.com/consolidation/annotated-command/issues", "source": "https://github.com/consolidation/annotated-command/tree/4.10.1"}, "time": "2024-12-13T19:55:40+00:00"}, {"name": "consolidation/config", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/consolidation/config.git", "reference": "597f8d7fbeef801736250ec10c3e190569b1b0ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/config/zipball/597f8d7fbeef801736250ec10c3e190569b1b0ae", "reference": "597f8d7fbeef801736250ec10c3e190569b1b0ae", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2 || ^3", "grasmash/expander": "^2.0.1 || ^3", "php": ">=7.1.3", "symfony/event-dispatcher": "^4 || ^5 || ^6"}, "require-dev": {"ext-json": "*", "phpunit/phpunit": ">=7.5.20", "squizlabs/php_codesniffer": "^3", "symfony/console": "^4 || ^5 || ^6", "symfony/yaml": "^4 || ^5 || ^6", "yoast/phpunit-polyfills": "^1"}, "suggest": {"symfony/event-dispatcher": "Required to inject configuration into Command options", "symfony/yaml": "Required to use Consolidation\\Config\\Loader\\YamlConfigLoader"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provide configuration services for a commandline tool.", "support": {"issues": "https://github.com/consolidation/config/issues", "source": "https://github.com/consolidation/config/tree/2.1.2"}, "time": "2022-10-06T17:48:03+00:00"}, {"name": "consolidation/filter-via-dot-access-data", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/consolidation/filter-via-dot-access-data.git", "reference": "cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/filter-via-dot-access-data/zipball/cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b", "reference": "cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2.0.0 || ^3.0.0", "php": ">=7.1.3"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Filter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "This project uses dflydev/dot-access-data to provide simple output filtering for applications built with annotated-command / Robo.", "support": {"source": "https://github.com/consolidation/filter-via-dot-access-data/tree/2.0.2"}, "time": "2021-12-30T03:56:08+00:00"}, {"name": "consolidation/log", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/consolidation/log.git", "reference": "c27a3beb36137c141ccbce0d89f64befb243c015"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/log/zipball/c27a3beb36137c141ccbce0d89f64befb243c015", "reference": "c27a3beb36137c141ccbce0d89f64befb243c015", "shasum": ""}, "require": {"php": ">=8.0.0", "psr/log": "^3", "symfony/console": "^5 || ^6 || ^7"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"platform": {"php": "8.2.17"}}, "autoload": {"psr-4": {"Consolidation\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Improved Psr-3 / Psr\\Log logger based on Symfony Console components.", "support": {"issues": "https://github.com/consolidation/log/issues", "source": "https://github.com/consolidation/log/tree/3.1.0"}, "time": "2024-04-04T23:50:25+00:00"}, {"name": "consolidation/output-formatters", "version": "4.6.0", "source": {"type": "git", "url": "https://github.com/consolidation/output-formatters.git", "reference": "5fd5656718d7068a02d046f418a7ba873d5abbfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/output-formatters/zipball/5fd5656718d7068a02d046f418a7ba873d5abbfe", "reference": "5fd5656718d7068a02d046f418a7ba873d5abbfe", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2 || ^3", "php": ">=7.1.3", "symfony/console": "^4 || ^5 || ^6 || ^7", "symfony/finder": "^4 || ^5 || ^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": "^7 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4 || ^5 || ^6 || ^7", "symfony/yaml": "^4 || ^5 || ^6 || ^7", "yoast/phpunit-polyfills": "^1"}, "suggest": {"symfony/var-dumper": "For using the var_dump formatter"}, "type": "library", "autoload": {"psr-4": {"Consolidation\\OutputFormatters\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Format text by applying transformations provided by plug-in formatters.", "support": {"issues": "https://github.com/consolidation/output-formatters/issues", "source": "https://github.com/consolidation/output-formatters/tree/4.6.0"}, "time": "2024-10-18T14:02:48+00:00"}, {"name": "consolidation/robo", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/consolidation/robo.git", "reference": "55a272370940607649e5c46eb173c5c54f7c166d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/robo/zipball/55a272370940607649e5c46eb173c5c54f7c166d", "reference": "55a272370940607649e5c46eb173c5c54f7c166d", "shasum": ""}, "require": {"consolidation/annotated-command": "^4.8.1", "consolidation/config": "^2.0.1", "consolidation/log": "^2.0.2 || ^3", "consolidation/output-formatters": "^4.1.2", "consolidation/self-update": "^2.0", "league/container": "^3.3.1 || ^4.0", "php": ">=8.0", "phpowermove/docblock": "^4.0", "symfony/console": "^6", "symfony/event-dispatcher": "^6", "symfony/filesystem": "^6", "symfony/finder": "^6", "symfony/process": "^6", "symfony/yaml": "^6"}, "conflict": {"codegyre/robo": "*"}, "require-dev": {"natxet/cssmin": "3.0.4", "patchwork/jsqueeze": "^2", "pear/archive_tar": "^1.4.4", "phpunit/phpunit": "^7.5.20 || ^8", "squizlabs/php_codesniffer": "^3.6", "yoast/phpunit-polyfills": "^0.2.0"}, "suggest": {"natxet/cssmin": "For minifying CSS files in taskMinify", "patchwork/jsqueeze": "For minifying JS files in taskMinify", "pear/archive_tar": "Allows tar archives to be created and extracted in taskPack and taskExtract, respectively.", "totten/lurkerlite": "For monitoring filesystem changes in taskWatch"}, "bin": ["robo"], "type": "library", "autoload": {"psr-4": {"Robo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Modern task runner", "support": {"issues": "https://github.com/consolidation/robo/issues", "source": "https://github.com/consolidation/robo/tree/4.0.6"}, "time": "2023-04-30T21:49:04+00:00"}, {"name": "consolidation/self-update", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/consolidation/self-update.git", "reference": "972a1016761c9b63314e040836a12795dff6953a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/self-update/zipball/972a1016761c9b63314e040836a12795dff6953a", "reference": "972a1016761c9b63314e040836a12795dff6953a", "shasum": ""}, "require": {"composer/semver": "^3.2", "php": ">=5.5.0", "symfony/console": "^2.8 || ^3 || ^4 || ^5 || ^6", "symfony/filesystem": "^2.5 || ^3 || ^4 || ^5 || ^6"}, "bin": ["scripts/release"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"SelfUpdate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provides a self:update command for Symfony Console applications.", "support": {"issues": "https://github.com/consolidation/self-update/issues", "source": "https://github.com/consolidation/self-update/tree/2.2.0"}, "time": "2023-03-18T01:37:41+00:00"}, {"name": "consolidation/site-alias", "version": "4.1.1", "source": {"type": "git", "url": "https://github.com/consolidation/site-alias.git", "reference": "aff6189aae17da813d23249cb2fc0fff33f26d40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-alias/zipball/aff6189aae17da813d23249cb2fc0fff33f26d40", "reference": "aff6189aae17da813d23249cb2fc0fff33f26d40", "shasum": ""}, "require": {"consolidation/config": "^1.2.1 || ^2 || ^3", "php": ">=7.4", "symfony/filesystem": "^5.4 || ^6 || ^7", "symfony/finder": "^5 || ^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": ">=7", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteAlias\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Manage alias records for local and remote sites.", "support": {"issues": "https://github.com/consolidation/site-alias/issues", "source": "https://github.com/consolidation/site-alias/tree/4.1.1"}, "time": "2024-12-13T19:05:11+00:00"}, {"name": "consolidation/site-process", "version": "5.4.2", "source": {"type": "git", "url": "https://github.com/consolidation/site-process.git", "reference": "e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-process/zipball/e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da", "reference": "e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da", "shasum": ""}, "require": {"consolidation/config": "^2 || ^3", "consolidation/site-alias": "^3 || ^4", "php": ">=8.0.14", "symfony/console": "^5.4 || ^6 || ^7", "symfony/process": "^6 || ^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteProcess\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A thin wrapper around the Symfony Process Component that allows applications to use the Site Alias library to specify the target for a remote call.", "support": {"issues": "https://github.com/consolidation/site-process/issues", "source": "https://github.com/consolidation/site-process/tree/5.4.2"}, "time": "2024-12-13T19:25:56+00:00"}, {"name": "cweagans/composer-patches", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0 || ~2.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "support": {"issues": "https://github.com/cweagans/composer-patches/issues", "source": "https://github.com/cweagans/composer-patches/tree/1.7.3"}, "time": "2022-12-20T22:53:13+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/a23a2bf4f31d3518f3ecb38660c95715dfead60f", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.3"}, "time": "2024-07-08T12:26:09+00:00"}, {"name": "doctrine/annotations", "version": "1.14.4", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "drupal/admin_toolbar", "version": "3.6.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/admin_toolbar.git", "reference": "3.6.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/admin_toolbar-3.6.1.zip", "reference": "3.6.1", "shasum": "40e8874bdf100de90e0eec6984be14f0ec7765b0"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "require-dev": {"drupal/admin_toolbar_tools": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.6.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON> (eme)", "homepage": "https://www.drupal.org/u/eme", "role": "Maintainer"}, {"name": "<PERSON><PERSON> (romainj)", "homepage": "https://www.drupal.org/u/romainj", "role": "Maintainer"}, {"name": "<PERSON> (adriancid)", "homepage": "https://www.drupal.org/u/adriancid", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON> (matio89)", "homepage": "https://www.drupal.org/u/matio89", "role": "Maintainer"}, {"name": "fethi.krout", "homepage": "https://www.drupal.org/user/3206765"}, {"name": "japerry", "homepage": "https://www.drupal.org/user/45640"}, {"name": "matio89", "homepage": "https://www.drupal.org/user/2320090"}, {"name": "musa.thomas", "homepage": "https://www.drupal.org/user/1213824"}, {"name": "r<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/370706"}], "description": "Provides a drop-down menu interface to the core Drupal Toolbar.", "homepage": "http://drupal.org/project/admin_toolbar", "keywords": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/admin_toolbar", "issues": "https://www.drupal.org/project/issues/admin_toolbar"}}, {"name": "drupal/allowed_formats", "version": "3.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/allowed_formats.git", "reference": "3.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/allowed_formats-3.0.1.zip", "reference": "3.0.1", "shasum": "9dfaed3ab8425ee94146914fdb492cefc6c6bb4d"}, "require": {"drupal/core": "^10.1 || ^11"}, "conflict": {"drupal/core": "<10.1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Northern Commerce (formerly Digital Echidna)", "homepage": "https://www.drupal.org/northern-commerce-formerly-digital-echidna", "role": "Supporting organization"}, {"name": "<PERSON> (nord102)", "homepage": "https://www.drupal.org/u/nord102", "role": "Maintainer"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/wunder", "role": "Supporting organization"}, {"name": "<PERSON><PERSON><PERSON> (floretan)", "homepage": "https://www.drupal.org/u/floretan", "role": "Maintainer"}], "description": "Hides info about the selected text format. The 'allowed formats' functionality has been moved to core since Drupal 10.1.0.", "homepage": "https://www.drupal.org/project/allowed_formats", "support": {"source": "http://cgit.drupalcode.org/allowed_formats", "issues": "https://www.drupal.org/project/issues/allowed_formats"}}, {"name": "drupal/block_content_permissions", "version": "1.11.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/block_content_permissions.git", "reference": "8.x-1.11"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/block_content_permissions-8.x-1.11.zip", "reference": "8.x-1.11", "shasum": "1ecb7330f69be30b6cf05f8682d1957c1aaf605e"}, "require": {"drupal/core": "^8 || ^9 || ^10"}, "suggest": {"drupal/block_region_permissions": "Block Region Permissions adds permissions for administering 'blocks' based on each theme's regions."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/joshua<PERSON><PERSON>", "role": "Maintainer"}], "description": "Block Content Permissions adds permissions for administering 'block content types' and 'block content'.", "homepage": "https://www.drupal.org/project/block_content_permissions", "support": {"source": "https://git.drupalcode.org/project/block_content_permissions", "issues": "https://www.drupal.org/project/issues/block_content_permissions"}}, {"name": "drupal/color_field", "version": "3.0.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/color_field.git", "reference": "3.0.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/color_field-3.0.2.zip", "reference": "3.0.2", "shasum": "2778e454798a59d3e9fd27f56a161562e2d1f0fe"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "require-dev": {"drupal/core-recommended": "^9 || ^10 || ^11", "drupal/feeds": "^3.0@beta", "drupal/token": "~1.3"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "targoo", "homepage": "https://www.drupal.org/user/431910", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/nickwilde", "role": "Maintainer"}, {"name": "targoo", "homepage": "https://www.drupal.org/user/431910"}], "description": "Provides a color field type to store the color value and opacity", "homepage": "https://www.drupal.org/project/color_field", "support": {"source": "https://git.drupalcode.org/project/color_field", "issues": "https://www.drupal.org/project/issues/color_field"}}, {"name": "drupal/colorbox", "version": "2.1.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/colorbox.git", "reference": "2.1.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/colorbox-2.1.4.zip", "reference": "2.1.4", "shasum": "a06e2ed6ab9b41a448aaa6a621560e9706564c76"}, "require": {"drupal/core": "^10 || ^11"}, "suggest": {"jackmoore/colorbox": "The Colorbox library is required to use the drupal/colorbox module."}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/paulm<PERSON>bben", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/neslee-canil-pinto", "role": "Former Maintainer"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/u/frjo", "role": "Former Maintainer"}], "description": "A light-weight, customizable lightbox plugin for jQuery.", "homepage": "https://www.drupal.org/project/colorbox", "support": {"source": "https://git.drupalcode.org/project/colorbox", "issues": "https://www.drupal.org/project/issues/colorbox"}}, {"name": "drupal/config_ignore", "version": "3.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/config_ignore.git", "reference": "8.x-3.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/config_ignore-8.x-3.3.zip", "reference": "8.x-3.3", "shasum": "4446811ecb023820a57c227d35c034e0d4363a70"}, "require": {"drupal/core": "^8.8 || ^9 || ^10 || ^11"}, "require-dev": {"drupal/config_filter": "^1.8||^2.2", "drush/drush": "^10 || ^11 || ^12"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/tlyngej", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/bircher", "role": "Maintainer"}, {"name": "tlyngej", "homepage": "https://www.drupal.org/user/413139"}], "description": "Ignore certain configuration during import and export.", "homepage": "http://drupal.org/project/config_ignore", "support": {"source": "https://git.drupalcode.org/project/config_ignore", "issues": "http://drupal.org/project/config_ignore"}}, {"name": "drupal/core", "version": "10.5.1", "source": {"type": "git", "url": "https://github.com/drupal/core.git", "reference": "551442fec1db69cf6eedb1601a348d8a6268060f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core/zipball/551442fec1db69cf6eedb1601a348d8a6268060f", "reference": "551442fec1db69cf6eedb1601a348d8a6268060f", "shasum": ""}, "require": {"asm89/stack-cors": "^2.3", "composer-runtime-api": "^2.1", "composer/semver": "^3.3", "doctrine/annotations": "^1.14", "egulias/email-validator": "^3.2.1|^4.0", "ext-date": "*", "ext-dom": "*", "ext-filter": "*", "ext-gd": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-simplexml": "*", "ext-spl": "*", "ext-tokenizer": "*", "ext-xml": "*", "guzzlehttp/guzzle": "^7.5", "guzzlehttp/psr7": "^2.4.5", "masterminds/html5": "^2.7", "mck89/peast": "^1.14", "pear/archive_tar": "^1.4.14", "php": ">=8.1.0", "psr/log": "^3.0", "sebastian/diff": "^4", "symfony/console": "^6.4", "symfony/dependency-injection": "^6.4", "symfony/event-dispatcher": "^6.4", "symfony/filesystem": "^6.4", "symfony/finder": "^6.4", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.4", "symfony/mailer": "^6.4", "symfony/mime": "^6.4", "symfony/polyfill-iconv": "^1.26", "symfony/process": "^6.4", "symfony/psr-http-message-bridge": "^2.1|^6.4", "symfony/routing": "^6.4", "symfony/serializer": "^6.4", "symfony/validator": "^6.4", "symfony/yaml": "^6.4", "twig/twig": "^3.15.0"}, "conflict": {"drush/drush": "<12.4.3"}, "replace": {"drupal/core-annotation": "self.version", "drupal/core-assertion": "self.version", "drupal/core-class-finder": "self.version", "drupal/core-datetime": "self.version", "drupal/core-dependency-injection": "self.version", "drupal/core-diff": "self.version", "drupal/core-discovery": "self.version", "drupal/core-event-dispatcher": "self.version", "drupal/core-file-cache": "self.version", "drupal/core-file-security": "self.version", "drupal/core-filesystem": "self.version", "drupal/core-front-matter": "self.version", "drupal/core-gettext": "self.version", "drupal/core-graph": "self.version", "drupal/core-http-foundation": "self.version", "drupal/core-php-storage": "self.version", "drupal/core-plugin": "self.version", "drupal/core-proxy-builder": "self.version", "drupal/core-render": "self.version", "drupal/core-serialization": "self.version", "drupal/core-transliteration": "self.version", "drupal/core-utility": "self.version", "drupal/core-uuid": "self.version", "drupal/core-version": "self.version"}, "suggest": {"ext-zip": "Needed to extend the plugin.manager.archiver service capability with the handling of files in the ZIP format."}, "type": "drupal-core", "extra": {"drupal-scaffold": {"file-mapping": {"[web-root]/.htaccess": "assets/scaffold/files/htaccess", "[web-root]/README.md": "assets/scaffold/files/drupal.README.md", "[web-root]/index.php": "assets/scaffold/files/index.php", "[web-root]/.csslintrc": "assets/scaffold/files/csslintrc", "[web-root]/robots.txt": "assets/scaffold/files/robots.txt", "[web-root]/update.php": "assets/scaffold/files/update.php", "[web-root]/web.config": "assets/scaffold/files/web.config", "[web-root]/INSTALL.txt": "assets/scaffold/files/drupal.INSTALL.txt", "[web-root]/.eslintignore": "assets/scaffold/files/eslintignore", "[web-root]/.eslintrc.json": "assets/scaffold/files/eslintrc.json", "[web-root]/.ht.router.php": "assets/scaffold/files/ht.router.php", "[web-root]/sites/README.txt": "assets/scaffold/files/sites.README.txt", "[project-root]/.editorconfig": "assets/scaffold/files/editorconfig", "[web-root]/example.gitignore": "assets/scaffold/files/example.gitignore", "[web-root]/themes/README.txt": "assets/scaffold/files/themes.README.txt", "[project-root]/.gitattributes": "assets/scaffold/files/gitattributes", "[web-root]/modules/README.txt": "assets/scaffold/files/modules.README.txt", "[web-root]/profiles/README.txt": "assets/scaffold/files/profiles.README.txt", "[web-root]/sites/example.sites.php": "assets/scaffold/files/example.sites.php", "[web-root]/sites/development.services.yml": "assets/scaffold/files/development.services.yml", "[web-root]/sites/example.settings.local.php": "assets/scaffold/files/example.settings.local.php", "[web-root]/sites/default/default.services.yml": "assets/scaffold/files/default.services.yml", "[web-root]/sites/default/default.settings.php": "assets/scaffold/files/default.settings.php"}}}, "autoload": {"files": ["includes/bootstrap.inc"], "psr-4": {"Drupal\\Core\\": "lib/Drupal/Core", "Drupal\\Component\\": "lib/Drupal/Component"}, "classmap": ["lib/Drupal.php", "lib/Drupal/Component/DependencyInjection/Container.php", "lib/Drupal/Component/DependencyInjection/PhpArrayContainer.php", "lib/Drupal/Component/FileCache/FileCacheFactory.php", "lib/Drupal/Component/Utility/Timer.php", "lib/Drupal/Component/Utility/Unicode.php", "lib/Drupal/Core/Cache/Cache.php", "lib/Drupal/Core/Cache/CacheBackendInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumTrait.php", "lib/Drupal/Core/Cache/CacheTagsInvalidatorInterface.php", "lib/Drupal/Core/Cache/DatabaseBackend.php", "lib/Drupal/Core/Cache/DatabaseCacheTagsChecksum.php", "lib/Drupal/Core/Database/Connection.php", "lib/Drupal/Core/Database/Database.php", "lib/Drupal/Core/Database/StatementInterface.php", "lib/Drupal/Core/DependencyInjection/Container.php", "lib/Drupal/Core/DrupalKernel.php", "lib/Drupal/Core/DrupalKernelInterface.php", "lib/Drupal/Core/Installer/InstallerRedirectTrait.php", "lib/Drupal/Core/Site/Settings.php", "lib/Drupal/Component/Datetime/Time.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal is an open source content management platform powering millions of websites and applications.", "support": {"source": "https://github.com/drupal/core/tree/10.5.1"}, "time": "2025-06-26T14:05:15+00:00"}, {"name": "drupal/core-composer-scaffold", "version": "10.5.1", "source": {"type": "git", "url": "https://github.com/drupal/core-composer-scaffold.git", "reference": "db17b59620ce1c142a34dc017d9e696ce4771e55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-composer-scaffold/zipball/db17b59620ce1c142a34dc017d9e696ce4771e55", "reference": "db17b59620ce1c142a34dc017d9e696ce4771e55", "shasum": ""}, "require": {"composer-plugin-api": "^2", "php": ">=7.3.0"}, "conflict": {"drupal-composer/drupal-scaffold": "*"}, "require-dev": {"composer/composer": "^1.8@stable"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\Scaffold\\Plugin", "branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\Scaffold\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A flexible Composer project scaffold builder.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-composer-scaffold/tree/10.5.1"}, "time": "2024-08-22T14:31:30+00:00"}, {"name": "drupal/core-project-message", "version": "10.5.1", "source": {"type": "git", "url": "https://github.com/drupal/core-project-message.git", "reference": "d1da83722735cb0f7ccabf9fef7b5607b442c3a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-project-message/zipball/d1da83722735cb0f7ccabf9fef7b5607b442c3a8", "reference": "d1da83722735cb0f7ccabf9fef7b5607b442c3a8", "shasum": ""}, "require": {"composer-plugin-api": "^2", "php": ">=7.3.0"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\ProjectMessage\\MessagePlugin"}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\ProjectMessage\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Adds a message after Composer installation.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-project-message/tree/11.1.8"}, "time": "2023-07-24T07:55:25+00:00"}, {"name": "drupal/core-recommended", "version": "10.5.1", "source": {"type": "git", "url": "https://github.com/drupal/core-recommended.git", "reference": "60b76ba11c2ae9088283a1e6963b929ca976f4fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-recommended/zipball/60b76ba11c2ae9088283a1e6963b929ca976f4fc", "reference": "60b76ba11c2ae9088283a1e6963b929ca976f4fc", "shasum": ""}, "require": {"asm89/stack-cors": "~v2.3.0", "composer/semver": "~3.4.3", "doctrine/annotations": "~1.14.4", "doctrine/deprecations": "~1.1.5", "doctrine/lexer": "~2.1.1", "drupal/core": "10.5.1", "egulias/email-validator": "~4.0.4", "guzzlehttp/guzzle": "~7.9.3", "guzzlehttp/promises": "~2.2.0", "guzzlehttp/psr7": "~2.7.1", "masterminds/html5": "~2.9.0", "mck89/peast": "~v1.17.0", "pear/archive_tar": "~1.5.0", "pear/console_getopt": "~v1.4.3", "pear/pear-core-minimal": "~v1.10.16", "pear/pear_exception": "~v1.0.2", "psr/cache": "~3.0.0", "psr/container": "~2.0.2", "psr/event-dispatcher": "~1.0.0", "psr/http-client": "~1.0.3", "psr/http-factory": "~1.1.0", "psr/log": "~3.0.2", "ralouphie/getallheaders": "~3.0.3", "sebastian/diff": "~4.0.6", "symfony/console": "~v6.4.21", "symfony/dependency-injection": "~v6.4.20", "symfony/deprecation-contracts": "~v3.5.1", "symfony/error-handler": "~v6.4.20", "symfony/event-dispatcher": "~v6.4.13", "symfony/event-dispatcher-contracts": "~v3.5.1", "symfony/filesystem": "~v6.4.13", "symfony/finder": "~v6.4.17", "symfony/http-foundation": "~v6.4.21", "symfony/http-kernel": "~v6.4.21", "symfony/mailer": "~v6.4.21", "symfony/mime": "~v6.4.21", "symfony/polyfill-ctype": "~v1.31.0", "symfony/polyfill-iconv": "~v1.31.0", "symfony/polyfill-intl-grapheme": "~v1.31.0", "symfony/polyfill-intl-idn": "~v1.31.0", "symfony/polyfill-intl-normalizer": "~v1.31.0", "symfony/polyfill-mbstring": "~v1.31.0", "symfony/polyfill-php83": "~v1.31.0", "symfony/process": "~v6.4.20", "symfony/psr-http-message-bridge": "~v6.4.13", "symfony/routing": "~v6.4.18", "symfony/serializer": "~v6.4.21", "symfony/service-contracts": "~v3.5.1", "symfony/string": "~v6.4.21", "symfony/translation-contracts": "~v3.5.1", "symfony/validator": "~v6.4.21", "symfony/var-dumper": "~v6.4.21", "symfony/var-exporter": "~v6.4.21", "symfony/yaml": "~v6.4.21", "twig/twig": "~v3.20.0"}, "conflict": {"webflo/drupal-core-strict": "*"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Core and its dependencies with known-compatible minor versions. Require this project INSTEAD OF drupal/core.", "support": {"source": "https://github.com/drupal/core-recommended/tree/10.5.1"}, "time": "2025-06-26T14:05:15+00:00"}, {"name": "drupal/crop", "version": "2.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/crop.git", "reference": "8.x-2.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/crop-8.x-2.4.zip", "reference": "8.x-2.4", "shasum": "be11fad0abf1d53544d35cb4ca6cedd8e91d2542"}, "require": {"drupal/core": "^9.3 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "woprrr", "homepage": "https://www.drupal.org/user/858604"}], "description": "Provides storage and API for image crops.", "homepage": "https://www.drupal.org/project/crop", "support": {"source": "https://git.drupalcode.org/project/crop", "issues": "https://www.drupal.org/project/issues/crop"}}, {"name": "drupal/ctools", "version": "4.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/ctools.git", "reference": "4.1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/ctools-4.1.0.zip", "reference": "4.1.0", "shasum": "69f5889cf557df9e55519390e6a95cfa31b67874"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-3.x": "3.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (EclipseGc)", "homepage": "https://www.drupal.org/u/eclipsegc", "role": "Maintainer"}, {"name": "<PERSON> (japerry)", "homepage": "https://www.drupal.org/u/japerry", "role": "Maintainer"}, {"name": "<PERSON> (tim.plunkett)", "homepage": "https://www.drupal.org/u/timplunkett", "role": "Maintainer"}, {"name": "<PERSON> (neclimdul)", "homepage": "https://www.drupal.org/u/neclimdul", "role": "Maintainer"}, {"name": "<PERSON> (da<PERSON>hn<PERSON>)", "homepage": "https://www.drupal.org/u/dawehner", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/26979"}, {"name": "neclimdul", "homepage": "https://www.drupal.org/user/48673"}, {"name": "sdboyer", "homepage": "https://www.drupal.org/user/146719"}, {"name": "sun", "homepage": "https://www.drupal.org/user/54136"}, {"name": "tim.plunkett", "homepage": "https://www.drupal.org/user/241634"}], "description": "Provides a number of utility and helper APIs for Drupal developers and site builders.", "homepage": "https://www.drupal.org/project/ctools", "support": {"source": "https://git.drupalcode.org/project/ctools", "issues": "https://www.drupal.org/project/issues/ctools"}}, {"name": "drupal/draggableviews", "version": "2.1.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/draggableviews.git", "reference": "2.1.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/draggableviews-2.1.4.zip", "reference": "2.1.4", "shasum": "09a484ed290cf0f52dfa399b4dfea8fd50cd403b"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "require-dev": {"drupal/draggableviews_demo": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (iStryker)", "homepage": "https://www.drupal.org/u/istryker", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (podarok)", "homepage": "https://www.drupal.org/u/podarok", "email": "<EMAIL>", "role": "Drupal 10+, maintenance."}, {"name": "<PERSON><PERSON> (ygerasimov)", "homepage": "https://www.drupal.org/u/ygerasimov", "email": "<EMAIL>", "role": "Ex Maintainer (D7)"}, {"name": "<PERSON><PERSON><PERSON> (sevi)", "homepage": "https://www.drupal.org/u/sevi", "role": "Ex Maintainer (D6)"}], "description": "DraggableViews module makes views draggable.", "homepage": "https://www.drupal.org/project/draggableviews", "support": {"source": "https://cgit.drupalcode.org/draggableviews", "issues": "https://www.drupal.org/project/issues/draggableviews"}}, {"name": "drupal/dropzonejs", "version": "2.11.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/dropzonejs.git", "reference": "8.x-2.11"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/dropzonejs-8.x-2.11.zip", "reference": "8.x-2.11", "shasum": "0fb4eff1bba2fd33850db0dfd9929ef4bd4569ee"}, "require": {"drupal/core": "^9.3 || ^10 || ^11"}, "require-dev": {"drupal/entity_browser": "^2.5"}, "suggest": {"enyo/dropzone": "Required to use drupal/dropzonejs. DropzoneJS is an open source library that provides drag’n’drop file uploads with image previews."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://drupal.org/u/slashrsm", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://drupal.org/u/chrfritsch", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://drupal.org/u/Primsi", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://drupal.org/u/jungle", "role": "Maintainer"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/1998478/committers", "role": "contributor"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/282629"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "wouters_f", "homepage": "https://www.drupal.org/user/721548"}, {"name": "zkday", "homepage": "https://www.drupal.org/user/888644"}], "description": "Drupal integration for DropzoneJS - An open source library that provides drag’n’drop file uploads with image previews.", "homepage": "https://www.drupal.org/project/dropzonejs", "keywords": ["DropzoneJS", "<PERSON><PERSON><PERSON>"], "support": {"source": "https://www.drupal.org/project/dropzonejs", "issues": "https://www.drupal.org/project/issues/dropzonejs", "#media": "http://drupal.slack.com"}}, {"name": "drupal/dropzonejs_eb_widget", "version": "2.11.0", "require": {"drupal/core": "^9.3 || ^10 || ^11", "drupal/dropzonejs": "*", "drupal/entity_browser": "*"}, "type": "metapackage", "extra": {"drupal": {"version": "8.x-2.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "chr.fritsch", "homepage": "https://www.drupal.org/user/2103716"}, {"name": "Drupal media CI", "homepage": "https://www.drupal.org/user/3057985"}, {"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "jungle", "homepage": "https://www.drupal.org/user/2919723"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/282629"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "wouters_f", "homepage": "https://www.drupal.org/user/721548"}, {"name": "zkday", "homepage": "https://www.drupal.org/user/888644"}], "description": "DropzoneJS Entity browser widget.", "homepage": "https://www.drupal.org/project/dropzonejs", "support": {"source": "https://git.drupalcode.org/project/dropzonejs"}}, {"name": "drupal/ds", "version": "3.30.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/ds.git", "reference": "8.x-3.30"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/ds-8.x-3.30.zip", "reference": "8.x-3.30", "shasum": "9022852edfbb540726338b018915c4d7fedfd989"}, "require": {"drupal/core": "^10.2 || ^11"}, "require-dev": {"drupal/devel": "5.x-dev", "drupal/field_group": "3.x-dev"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.30", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/172527", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://realize.be/", "role": "Maintainer"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/591438", "role": "Maintainer"}, {"name": "swentel", "homepage": "https://www.drupal.org/user/107403"}], "description": "Extend the display options for every entity type.", "homepage": "https://www.drupal.org/project/ds", "keywords": ["drupal", "layout", "php"], "support": {"source": "http://git.drupal.org/project/ds.git", "issues": "https://www.drupal.org/project/issues/ds", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/editor_advanced_link", "version": "2.3.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/editor_advanced_link.git", "reference": "2.3.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/editor_advanced_link-2.3.1.zip", "reference": "2.3.1", "shasum": "5ceb0eb21a4015389cfe77fff9e7c3d53ef41667"}, "require": {"drupal/core": "^10.5 || ^11.2"}, "require-dev": {"drupal/ckeditor": "*", "phpro/grumphp": "^2.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.3.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/931394"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/673120"}], "description": "Editor Advanced link", "homepage": "https://www.drupal.org/project/editor_advanced_link", "support": {"source": "https://git.drupalcode.org/project/editor_advanced_link"}}, {"name": "drupal/embed", "version": "1.10.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/embed.git", "reference": "8.x-1.10"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/embed-8.x-1.10.zip", "reference": "8.x-1.10", "shasum": "4ac5be0c3a421851c9c60ed82e3bc497be88e61c"}, "require": {"drupal/core": "^10.2 || ^11"}, "require-dev": {"drupal/ckeditor": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.10", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "cs_shadow", "homepage": "https://www.drupal.org/user/2828287"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "devin <PERSON>lson", "homepage": "https://www.drupal.org/user/290182"}, {"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}], "description": "Provides a framework for different types of embeds in text editors.", "homepage": "https://www.drupal.org/project/embed", "support": {"source": "https://git.drupalcode.org/project/embed"}}, {"name": "drupal/entity_browser", "version": "2.13.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_browser.git", "reference": "8.x-2.13"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_browser-8.x-2.13.zip", "reference": "8.x-2.13", "shasum": "9c31caed2602a5cb582cb4f1e1feaac44cbbc21b"}, "require": {"drupal/core": "^10.2 || ^11"}, "conflict": {"drupal/media_entity": "1.*"}, "require-dev": {"drupal/embed": "^1.0", "drupal/entity_embed": "^1.0", "drupal/entity_reference_revisions": "^1.0", "drupal/entityqueue": "^1.0", "drupal/inline_entity_form": "^1 || ^3", "drupal/paragraphs": "^1.0", "drupal/search_api": "^1.0", "drupal/token": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.13", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/slashrsm", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/primsi", "role": "Maintainer"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/1943336/committers", "role": "contributor"}, {"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "marcingy", "homepage": "https://www.drupal.org/user/77320"}, {"name": "oknate", "homepage": "https://www.drupal.org/user/471638"}, {"name": "primsi", "homepage": "https://www.drupal.org/user/282629"}, {"name": "samuel.mortenson", "homepage": "https://www.drupal.org/user/2582268"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}], "description": "Entity browsing and selecting component.", "homepage": "https://drupal.org/project/entity_browser", "support": {"source": "https://git.drupalcode.org/project/entity_browser", "issues": "https://www.drupal.org/project/issues/entity_browser", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/entity_clone", "version": "dev-1.x", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_clone.git", "reference": "36a2dae4c7c08bbd09a1017d102ef2360eb6e460"}, "require": {"drupal/core": "^8 || ^9 || ^10"}, "require-dev": {"drupal/entity_browser": "2.x-dev", "drupal/entity_usage": "2.x-dev", "drupal/paragraphs": "^1.0", "drupal/search_api": "~1.0"}, "type": "drupal-module", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}, "drupal": {"version": "8.x-1.x-dev", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Dev releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "colan", "homepage": "https://www.drupal.org/user/58704"}, {"name": "joevagyok", "homepage": "https://www.drupal.org/user/2876343"}, {"name": "nick<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3094661"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3418561"}, {"name": "upchuk", "homepage": "https://www.drupal.org/user/1885838"}, {"name": "vpeltot", "homepage": "https://www.drupal.org/user/1361586"}], "description": "Add a clone action for all entities.", "homepage": "https://drupal.org/project/entity_clone", "support": {"source": "https://git.drupalcode.org/project/entity_clone"}}, {"name": "drupal/entity_embed", "version": "1.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_embed.git", "reference": "8.x-1.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_embed-8.x-1.7.zip", "reference": "8.x-1.7", "shasum": "64b7226390198572cdea0dd2227585f74338c773"}, "require": {"drupal/core": "^10.3 || ^11", "drupal/embed": "^1.10"}, "require-dev": {"drupal/entity_browser": "^2.11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "cs_shadow", "homepage": "https://www.drupal.org/user/2828287"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "devin <PERSON>lson", "homepage": "https://www.drupal.org/user/290182"}, {"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "oknate", "homepage": "https://www.drupal.org/user/471638"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "wim leers", "homepage": "https://www.drupal.org/user/99777"}], "description": "Allows any entity to be embedded within a text area using a WYSIWYG editor.", "homepage": "https://www.drupal.org/project/entity_embed", "support": {"source": "https://git.drupalcode.org/project/entity_embed"}}, {"name": "drupal/entity_reference_revisions", "version": "1.12.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_reference_revisions.git", "reference": "8.x-1.12"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_reference_revisions-8.x-1.12.zip", "reference": "8.x-1.12", "shasum": "2a2ff8617c7ce01b56df1caaf0a563da04948e26"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "require-dev": {"drupal/diff": "^1 || ^2"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.12", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/514222"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}], "description": "Entity Reference Revisions", "homepage": "https://www.drupal.org/project/entity_reference_revisions", "support": {"source": "https://git.drupalcode.org/project/entity_reference_revisions"}}, {"name": "drupal/field_group", "version": "3.6.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/field_group.git", "reference": "8.x-3.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/field_group-8.x-3.6.zip", "reference": "8.x-3.6", "shasum": "427c0a65dc1936e69e60c120776056cfe5b43e86"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "require-dev": {"drupal/jquery_ui_accordion": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "hydra", "homepage": "https://www.drupal.org/user/647364"}, {"name": "joevagyok", "homepage": "https://www.drupal.org/user/2876343"}, {"name": "jyve", "homepage": "https://www.drupal.org/user/591438"}, {"name": "nils.destoop", "homepage": "https://www.drupal.org/user/361625"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/322618"}, {"name": "swentel", "homepage": "https://www.drupal.org/user/107403"}], "description": "Provides the field_group module.", "homepage": "https://www.drupal.org/project/field_group", "support": {"source": "https://git.drupalcode.org/project/field_group", "issues": "https://www.drupal.org/project/issues/field_group"}}, {"name": "drupal/file_browser", "version": "1.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/file_browser.git", "reference": "8.x-1.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/file_browser-8.x-1.4.zip", "reference": "8.x-1.4", "shasum": "e781413c5aaa8d5549bfdc9d1b4c12561708b36a"}, "require": {"drupal/core": "~8 || ~9 || ~10", "drupal/dropzonejs": "^2", "drupal/dropzonejs_eb_widget": "*", "drupal/embed": "^1", "drupal/entity_browser": "^2 || ^1", "drupal/entity_embed": "^1"}, "require-dev": {"drupal/coder": "~8.3"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "samuel.mortenson", "homepage": "https://www.drupal.org/user/2582268"}], "description": "This module provides a default Entity Browser that lets you browse and select your files in a nice-looking, mobile-ready Masonry based interface, and upload files using the Dropzonejs module.", "homepage": "https://www.drupal.org/project/file_browser", "support": {"source": "https://git.drupalcode.org/project/file_browser"}}, {"name": "drupal/file_replace", "version": "1.5.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/file_replace.git", "reference": "8.x-1.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/file_replace-8.x-1.5.zip", "reference": "8.x-1.5", "shasum": "3c02cfd3feba04623f0dcc298039850bef7256af"}, "require": {"drupal/core": "^10.3 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "b<PERSON>la", "homepage": "https://www.drupal.org/user/3366066"}, {"name": "casey", "homepage": "https://www.drupal.org/user/32403"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3558376"}], "description": "Allows to replace files", "homepage": "https://www.drupal.org/project/file_replace", "support": {"source": "https://git.drupalcode.org/project/file_replace"}}, {"name": "drupal/flippy", "version": "2.0.0-beta1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/flippy.git", "reference": "2.0.0-beta1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/flippy-2.0.0-beta1.zip", "reference": "2.0.0-beta1", "shasum": "7f396233f10cded43d57a320d9f26c96a8322dc4"}, "require": {"drupal/core": "^9.1 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.0-beta1", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "Anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "eaton", "homepage": "https://www.drupal.org/user/16496"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "rli", "homepage": "https://www.drupal.org/user/633216"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}], "description": "Allows administrators to define custom pagers for navigation in lists of nodes.", "homepage": "https://www.drupal.org/project/flippy", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/flippy", "issues": "https://www.drupal.org/project/issues/flippy"}}, {"name": "drupal/fontawesome", "version": "2.26.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/fontawesome.git", "reference": "8.x-2.26"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/fontawesome-8.x-2.26.zip", "reference": "8.x-2.26", "shasum": "22e67458e1ebc274c3a8b494ce2c4056a3d2af29"}, "require": {"drupal/core": "^9.4 || ^10"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.26", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "daniel.moberly", "homepage": "https://www.drupal.org/user/1160788"}, {"name": "truls1502", "homepage": "https://www.drupal.org/user/325866"}], "description": "The web's most popular icon set and toolkit.", "homepage": "https://www.drupal.org/project/fontawesome", "support": {"source": "https://git.drupalcode.org/project/fontawesome", "issues": "https://drupal.org/project/issues/fontawesome"}}, {"name": "drupal/image_widget_crop", "version": "2.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/image_widget_crop.git", "reference": "8.x-2.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/image_widget_crop-8.x-2.4.zip", "reference": "8.x-2.4", "shasum": "6fcf4641d730bf1f5afce2a95d58f76094c72d1b"}, "require": {"drupal/core": "^8 || ^9 || ^10", "drupal/crop": "^1 || ^2"}, "require-dev": {"drupal/bartik": "^1", "drupal/crop": "*", "drupal/ctools": "^3", "drupal/entity_browser": "^2", "drupal/inline_entity_form": "^1"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/woprrr", "role": "Maintainer"}, {"name": "Drupal media CI", "homepage": "https://www.drupal.org/user/3057985"}, {"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "woprrr", "homepage": "https://www.drupal.org/user/858604"}], "description": "Provides an interface for using the features of the Crop API.", "homepage": "https://www.drupal.org/project/image_widget_crop", "keywords": ["Crop", "<PERSON><PERSON><PERSON>", "Drupal Media"], "support": {"source": "https://www.drupal.org/project/image_widget_crop", "issues": "https://www.drupal.org/project/issues/image_widget_crop", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/inline_entity_form", "version": "1.0.0-rc17", "source": {"type": "git", "url": "https://git.drupalcode.org/project/inline_entity_form.git", "reference": "8.x-1.0-rc17"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/inline_entity_form-8.x-1.0-rc17.zip", "reference": "8.x-1.0-rc17", "shasum": "626622e01cf7a2d2977fdc06ae09afd5a814e09b"}, "require": {"drupal/core": "^8.8 || ^9 || ^10", "php": ">=7.1"}, "require-dev": {"drupal/entity_reference_revisions": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-rc17", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "RC releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "b<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/86106"}, {"name": "centarro", "homepage": "https://www.drupal.org/user/3661446"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/99340"}, {"name": "dww", "homepage": "https://www.drupal.org/user/46549"}, {"name": "geek-merlin", "homepage": "https://www.drupal.org/user/229048"}, {"name": "joa<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/107701"}, {"name": "j<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/972218"}, {"name": "oknate", "homepage": "https://www.drupal.org/user/471638"}, {"name": "ram4nd", "homepage": "https://www.drupal.org/user/601534"}, {"name": "rszrama", "homepage": "https://www.drupal.org/user/49344"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "webflo", "homepage": "https://www.drupal.org/user/254778"}], "description": "Provides a widget for inline management (creation, modification, removal) of referenced entities.", "homepage": "https://www.drupal.org/project/inline_entity_form", "support": {"source": "https://git.drupalcode.org/project/inline_entity_form"}}, {"name": "drupal/link_attributes", "version": "2.1.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/link_attributes.git", "reference": "2.1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/link_attributes-2.1.1.zip", "reference": "2.1.1", "shasum": "fc71571fc41adeda6b39ccefd3c8e21955c0d96f"}, "require": {"drupal/core": "^9 || ^10 || ^11", "php": ">=8.0"}, "require-dev": {"drupal/linkit": "~6 || ~7"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/395439"}], "description": "Provides a widget to allow settings of link attributes for menu links.", "homepage": "https://www.drupal.org/project/link_attributes", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/link_attributes", "issues": "https://www.drupal.org/project/issues/link_attributes"}}, {"name": "drupal/menu_admin_per_menu", "version": "1.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/menu_admin_per_menu.git", "reference": "8.x-1.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/menu_admin_per_menu-8.x-1.7.zip", "reference": "8.x-1.7", "shasum": "ce9faed09c95de7c7a57653aab66c5afc3e946a2"}, "require": {"drupal/core": "^10.2 || ^11.0 || ^12"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/410199"}, {"name": "jero<PERSON>", "homepage": "https://www.drupal.org/user/2228934"}, {"name": "jonas139", "homepage": "https://www.drupal.org/user/2873401"}, {"name": "mkdok", "homepage": "https://www.drupal.org/user/3308753"}], "description": "Allows to give roles per menu admin permissions without giving them full administer menu permission.", "homepage": "https://www.drupal.org/project/menu_admin_per_menu", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/menu_admin_per_menu", "issues": "https://drupal.org/project/issues/menu_admin_per_menu"}}, {"name": "drupal/menu_breadcrumb", "version": "2.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/menu_breadcrumb.git", "reference": "2.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/menu_breadcrumb-2.0.0.zip", "reference": "2.0.0", "shasum": "016f2eead9cf9d4bfdf4b36fca5e3c8a8e8d0f1e"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "aaron", "homepage": "https://www.drupal.org/user/33420"}, {"name": "gdevlugt", "homepage": "https://www.drupal.org/user/167273"}, {"name": "r<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3196607"}, {"name": "RyanPrice", "homepage": "https://www.drupal.org/user/873848"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1159692"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/76026"}], "description": "Create breadcrumbs from nested menu titles and/or taxonomy membership..", "homepage": "https://www.drupal.org/project/menu_breadcrumb", "support": {"source": "https://git.drupalcode.org/project/menu_breadcrumb", "issues": "https://www.drupal.org/project/issues/menu_breadcrumb"}}, {"name": "drupal/menu_multilingual", "version": "1.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/menu_multilingual.git", "reference": "8.x-1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/menu_multilingual-8.x-1.0.zip", "reference": "8.x-1.0", "shasum": "b533af65081e31031cb28b2f51a5f4ed040d9cbe"}, "require": {"drupal/core": "^9 || ^10"}, "require-dev": {"drupal/context": "^5.0@RC", "drupal/menu_block": "^1.9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "vlad.dancer", "homepage": "https://www.drupal.org/u/vladdancer"}, {"name": "matsbla", "homepage": "https://www.drupal.org/u/matsbla"}], "description": "Filter menu links by current language.", "homepage": "https://www.drupal.org/project/menu_multilingual", "support": {"source": "http://git.drupal.org/project/menu_multilingual.git", "issues": "https://www.drupal.org/project/issues/menu_multilingual"}}, {"name": "drupal/metatag", "version": "2.1.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/metatag.git", "reference": "2.1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/metatag-2.1.1.zip", "reference": "2.1.1", "shasum": "94f272db388cbf1e4df775a2677f8c35818c9382"}, "require": {"drupal/core": "^9.4 || ^10 || ^11", "drupal/token": "^1.0", "php": ">=8.0"}, "require-dev": {"drupal/forum": "1.x-dev", "drupal/hal": "^1 || ^2 || ^9", "drupal/metatag_dc": "*", "drupal/metatag_open_graph": "*", "drupal/page_manager": "^4.0", "drupal/redirect": "^1.0", "ergebnis/composer-normalize": "*", "mpyw/phpunit-patch-serializable-comparison": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "composer-normalize": {"indent-size": 2, "indent-style": "space"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "See contributors", "homepage": "https://www.drupal.org/node/640498/committers", "role": "Developer"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}], "description": "Manage meta tags for all entities.", "homepage": "https://www.drupal.org/project/metatag", "keywords": ["<PERSON><PERSON><PERSON>", "seo"], "support": {"source": "https://git.drupalcode.org/project/metatag", "issues": "https://www.drupal.org/project/issues/metatag", "docs": "https://www.drupal.org/docs/8/modules/metatag"}}, {"name": "drupal/paragraphs", "version": "1.19.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/paragraphs.git", "reference": "8.x-1.19"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/paragraphs-8.x-1.19.zip", "reference": "8.x-1.19", "shasum": "831a81a11eac419e8410db45efef5b283c4d117c"}, "require": {"drupal/core": "^10.2 || ^11", "drupal/entity_reference_revisions": "~1.3"}, "require-dev": {"drupal/block_field": "1.x-dev", "drupal/diff": "1.x-dev", "drupal/entity_browser": "2.x-dev", "drupal/entity_usage": "2.x-dev", "drupal/feeds": "^3", "drupal/field_group": "3.x-dev", "drupal/inline_entity_form": "3.x-dev", "drupal/paragraphs-paragraphs_library": "*", "drupal/replicate": "1.x-dev", "drupal/search_api": "^1", "drupal/search_api_db": "*"}, "suggest": {"drupal/entity_browser": "Recommended for an improved user experience when using the Paragraphs library module"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.19", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "frans", "homepage": "https://www.drupal.org/user/514222"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "j<PERSON>ller", "homepage": "https://www.drupal.org/user/99012"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}, {"name": "primsi", "homepage": "https://www.drupal.org/user/282629"}], "description": "Enables the creation of Paragraphs entities.", "homepage": "https://www.drupal.org/project/paragraphs", "support": {"source": "https://git.drupalcode.org/project/paragraphs"}}, {"name": "drupal/paragraphs_asymmetric_translation_widgets", "version": "1.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/paragraphs_asymmetric_translation_widgets.git", "reference": "8.x-1.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/paragraphs_asymmetric_translation_widgets-8.x-1.4.zip", "reference": "8.x-1.4", "shasum": "3b09fb13d2bd0bb351cf98fdae1b27eab8032984"}, "require": {"drupal/core": "^9.3 || ^10 || ^11", "drupal/paragraphs": "~1.15"}, "require-dev": {"drupal/entity_browser": "2.x-dev", "drupal/entity_usage": "2.x-dev", "drupal/paragraphs": "*", "drupal/search_api": "~1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "ef<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1009348"}, {"name": "esolitos", "homepage": "https://www.drupal.org/user/1567500"}, {"name": "grayle", "homepage": "https://www.drupal.org/user/3145497"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/959536"}, {"name": "r<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1414312"}, {"name": "weseze", "homepage": "https://www.drupal.org/user/417521"}], "description": "Extends the paragraphs field widgets to support asymmetric translations.", "homepage": "https://www.drupal.org/project/paragraphs_asymmetric_translation_widgets", "support": {"source": "https://git.drupalcode.org/project/paragraphs_asymmetric_translation_widgets"}}, {"name": "drupal/pathauto", "version": "1.13.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/pathauto.git", "reference": "8.x-1.13"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/pathauto-8.x-1.13.zip", "reference": "8.x-1.13", "shasum": "e64b5a82cf1b8ab48bce400b21ae6fc99c8078fd"}, "require": {"drupal/core": "^9.4 || ^10 || ^11", "drupal/ctools": "*", "drupal/token": "*"}, "require-dev": {"drupal/forum": "*"}, "suggest": {"drupal/redirect": "When installed Pathauto will provide a new \"Update Action\" in case your URLs change. This is the recommended update action and is considered the best practice for SEO and usability."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.13", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "Freso", "homepage": "https://www.drupal.org/user/27504"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}], "description": "Provides a mechanism for modules to automatically generate aliases for the content they manage.", "homepage": "https://www.drupal.org/project/pathauto", "support": {"source": "https://cgit.drupalcode.org/pathauto", "issues": "https://www.drupal.org/project/issues/pathauto", "documentation": "https://www.drupal.org/docs/8/modules/pathauto"}}, {"name": "drupal/redirect", "version": "1.11.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/redirect.git", "reference": "8.x-1.11"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/redirect-8.x-1.11.zip", "reference": "8.x-1.11", "shasum": "7df8b3524bbde07d254216039636947a689140ef"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "kristen pol", "homepage": "https://www.drupal.org/user/8389"}, {"name": "pifagor", "homepage": "https://www.drupal.org/user/2375692"}], "description": "Allows users to redirect from old URLs to new URLs.", "homepage": "https://www.drupal.org/project/redirect", "support": {"source": "https://git.drupalcode.org/project/redirect"}}, {"name": "drupal/redis", "version": "1.9.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/redis.git", "reference": "8.x-1.9"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/redis-8.x-1.9.zip", "reference": "8.x-1.9", "shasum": "77e2e8ddb95be08f3fe9f74182c7ff0476e15674"}, "require": {"drupal/core": "^9.3 || ^10 || ^11"}, "suggest": {"ext-redis": "Required to use the PhpRedis as redis driver (^4.0|^5.0).", "ext-relay": "Required to use the Relay as Redis driver (^0.5|^1.0).", "predis/predis": "Required to use the Predis as redis driver (^1.1|^2.0)."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.9", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "autoload": {"psr-4": {"Drupal\\redis\\": "src"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "greg.1.anderson", "homepage": "https://www.drupal.org/user/438598"}, {"name": "kporras07", "homepage": "https://www.drupal.org/user/1349780"}, {"name": "pou<PERSON>", "homepage": "https://www.drupal.org/user/240164"}], "description": "Integration of Drupal with the Redis key-value store.", "homepage": "https://www.drupal.org/project/redis", "support": {"source": "https://git.drupalcode.org/project/redis"}}, {"name": "drupal/rename_admin_paths", "version": "3.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/rename_admin_paths.git", "reference": "3.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/rename_admin_paths-3.0.0.zip", "reference": "3.0.0", "shasum": "8b91f18d38aec5e651a5cfc1805e4d4559ada617"}, "require": {"drupal/core": ">=10", "php": ">=8.1"}, "require-dev": {"mglaman/phpstan-drupal": "^1.2", "phpstan/extension-installer": "^1.3", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.5", "phpstan/phpstan-webmozart-assert": "^1.2", "thecodingmachine/phpstan-strict-rules": "^1"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0"], "authors": [{"name": "ptmkenny", "homepage": "https://www.drupal.org/user/97885"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/410831"}, {"name": "rj<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3457245"}, {"name": "slo<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3516667"}], "description": "This module helps secure the Dr<PERSON><PERSON> backend by overriding the admin path.", "homepage": "https://www.drupal.org/project/rename_admin_paths", "keywords": ["admin", "guess", "override", "protect", "security"], "support": {"source": "https://git.drupalcode.org/project/rename_admin_paths"}}, {"name": "drupal/simple_sitemap", "version": "4.2.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/simple_sitemap.git", "reference": "4.2.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/simple_sitemap-4.2.2.zip", "reference": "4.2.2", "shasum": "1f9c9197d37450fb347a4fa3f10191f5f4b5ef13"}, "require": {"drupal/core": "^10.2 || ^11", "ext-xmlwriter": "*"}, "conflict": {"drush/drush": "<12.5.1"}, "require-dev": {"drupal/paragraphs": "^1.18"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.2.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON> (gbyte)", "homepage": "https://www.drupal.org/u/gbyte", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "walkingdexter", "homepage": "https://www.drupal.org/user/3251330"}], "description": "Creates a standard conform hreflang XML sitemap of the site content and provides a framework for developing other sitemap types.", "homepage": "https://drupal.org/project/simple_sitemap", "support": {"source": "https://cgit.drupalcode.org/simple_sitemap", "issues": "https://drupal.org/project/issues/simple_sitemap"}}, {"name": "drupal/sitemap", "version": "2.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/sitemap.git", "reference": "8.x-2.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/sitemap-8.x-2.0.zip", "reference": "8.x-2.0", "shasum": "1e1db0da34ff07318cd2179b392d6495da49a0f4"}, "require": {"drupal/core": "^10.2 || ^11"}, "require-dev": {"drupal/book": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/nafes", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/akalata", "role": "Co-maintainer"}, {"name": "<EMAIL>", "homepage": "https://www.drupal.org/u/killeswww.drop.org", "role": "Co-maintainer"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/3295/committers", "role": "contributor"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2489926"}, {"name": "ridefree", "homepage": "https://www.drupal.org/user/49148"}], "description": "Display a sitemap.", "homepage": "http://drupal.org/project/sitemap", "support": {"source": "http://cgit.drupalcode.org/sitemap", "issues": "http://drupal.org/project/issues/sitemap"}}, {"name": "drupal/svg_image", "version": "3.2.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/svg_image.git", "reference": "3.2.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/svg_image-3.2.1.zip", "reference": "3.2.1", "shasum": "4623b9d0de4c624857df10daaa8c68793942ad87"}, "require": {"drupal/core": "^10.3 || ^11", "enshrined/svg-sanitize": ">=0.15 <1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.2.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/imyaro", "role": "Maintainer"}, {"name": "See contributors", "homepage": "https://www.drupal.org/node/2887125/committers"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2870933"}, {"name": "mably", "homepage": "https://www.drupal.org/user/3375160"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}], "description": "Overrides the standard image formatter and widget to support SVG files.", "homepage": "https://drupal.org/project/svg_image", "support": {"source": "https://git.drupalcode.org/project/svg_image", "issues": "https://www.drupal.org/project/issues/svg_image"}}, {"name": "drupal/svg_image_field", "version": "2.3.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/svg_image_field.git", "reference": "2.3.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/svg_image_field-2.3.4.zip", "reference": "2.3.4", "shasum": "c4452fb1b63b7bb15c807748fe30bd76e641e718"}, "require": {"drupal/core": "^9.3 || ^10 || ^11", "enshrined/svg-sanitize": "~0.15", "php": ">=8.0"}, "require-dev": {"drupal/stage_file_proxy": "^2.0 || ^3.0", "drush/drush": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.3.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "See contributors", "homepage": "https://www.drupal.org/node/2863622/committers"}, {"name": "dunot", "homepage": "https://www.drupal.org/user/3562626"}, {"name": "jwilson3", "homepage": "https://www.drupal.org/user/220177"}, {"name": "mandclu", "homepage": "https://www.drupal.org/user/52136"}, {"name": "romychvk", "homepage": "https://www.drupal.org/user/361734"}, {"name": "shmel210", "homepage": "https://www.drupal.org/user/2600028"}], "description": "Provides SVG Image field.", "homepage": "https://www.drupal.org/project/svg_image_field", "support": {"source": "https://git.drupalcode.org/project/svg_image_field", "issues": "https://www.drupal.org/project/issues/svg_image_field"}}, {"name": "drupal/token", "version": "1.15.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/token.git", "reference": "8.x-1.15"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/token-8.x-1.15.zip", "reference": "8.x-1.15", "shasum": "5916fbccc86458a5f51e71f832ac70ff4c84ebdf"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "require-dev": {"drupal/book": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.15", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "eaton", "homepage": "https://www.drupal.org/user/16496"}, {"name": "fago", "homepage": "https://www.drupal.org/user/16747"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Provides a user interface for the Token API, some missing core tokens.", "homepage": "https://www.drupal.org/project/token", "support": {"source": "https://git.drupalcode.org/project/token"}}, {"name": "drupal/tvi", "version": "dev-2.0.x", "source": {"type": "git", "url": "https://git.drupalcode.org/project/tvi.git", "reference": "d5349cf21299dca972bf95147609c7e4fbe9612b"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"branch-alias": {"dev-2.0.x": "2.0.x-dev"}, "drupal": {"version": "2.0.0-alpha2+6-dev", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Dev releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON> (kevinquillen)", "homepage": "https://www.drupal.org/u/kevin<PERSON>llen", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/931394"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/317279"}, {"name": "mi<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1118796"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3437973"}], "description": "Enables you to change the presentation of taxonomy term pages with Views, allowing you to create multiple Views on the same path with different output.", "homepage": "http://drupal.org/project/tvi", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/tvi", "issues": "http://drupal.org/project/issues/tvi"}}, {"name": "drupal/twig_tweak", "version": "3.4.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/twig_tweak.git", "reference": "3.4.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/twig_tweak-3.4.1.zip", "reference": "3.4.1", "shasum": "ceaa5ea8f357ce8827c728f22871265f0f7cd74f"}, "require": {"drupal/core": "^10.3 || ^11.0", "ext-json": "*", "php": ">=8.1", "twig/twig": "^3.10.3"}, "suggest": {"symfony/var-dumper": "Better dump() function for debugging Twig variables"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.4.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "chi", "homepage": "https://www.drupal.org/user/556138"}], "description": "A Twig extension with some useful functions and filters for Drup<PERSON> development.", "homepage": "https://www.drupal.org/project/twig_tweak", "keywords": ["<PERSON><PERSON><PERSON>", "Twig"], "support": {"source": "https://git.drupalcode.org/project/twig_tweak", "issues": "https://www.drupal.org/project/issues/twig_tweak"}}, {"name": "drupal/twig_vardumper", "version": "3.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/twig_vardumper.git", "reference": "3.2.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/twig_vardumper-3.2.0.zip", "reference": "3.2.0", "shasum": "2e69f04c46354888aea9814f407210f1069ae1bb"}, "require": {"drupal/core": "^9 || ^10 || ^11", "symfony/var-dumper": "^5|^6|^7"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.2.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON> a.k.a keopx", "homepage": "https://www.drupal.org/user/377362", "email": "<EMAIL>"}], "description": "Twig vardumper provides a better dump() and vardumper() function that can help you debug Twig variables.", "homepage": "https://www.drupal.org/project/twig_vardumper", "keywords": ["Development", "<PERSON><PERSON><PERSON>", "Twig"], "support": {"source": "https://git.drupalcode.org/project/twig_vardumper", "issues": "https://www.drupal.org/project/issues/twig_vardumper"}}, {"name": "drupal/unpublished_node_permissions", "version": "1.6.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/unpublished_node_permissions.git", "reference": "8.x-1.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/unpublished_node_permissions-8.x-1.6.zip", "reference": "8.x-1.6", "shasum": "3201e897f4661a30e0be713f3fd16980acf614cc"}, "require": {"drupal/core": "^8.9 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON> (Islanderweedy)", "homepage": "https://www.drupal.org/u/islanderweedy", "role": "Maintainer"}, {"name": "<PERSON><PERSON> (Frans)", "homepage": "https://www.drupal.org/u/frans", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (Fabsgugu)", "homepage": "https://www.drupal.org/u/fabsgugu", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (jeroen.b)", "homepage": "https://www.drupal.org/u/jeroenb", "role": "Maintainer"}, {"name": "<PERSON> (matglas86)", "homepage": "https://www.drupal.org/u/matglas86", "role": "Maintainer"}, {"name": "<PERSON> (AstonVictor)", "homepage": "https://www.drupal.org/u/astonvictor", "role": "Maintainer"}], "description": "Creates permisisons per node content type to control access to unpublished nodes per content type.", "homepage": "https://www.drupal.org/project/unpublished_node_permissions", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/unpublished_node_permissions", "issues": "https://www.drupal.org/project/issues/unpublished_node_permissions"}}, {"name": "drupal/views_bulk_operations", "version": "4.3.4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/views_bulk_operations.git", "reference": "4.3.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/views_bulk_operations-4.3.4.zip", "reference": "4.3.4", "shasum": "c0974356f26d49ad9e99450e9db9650de94c6010"}, "require": {"drupal/core": "^10.3 || ^11"}, "conflict": {"drush/drush": "<12.5.1"}, "require-dev": {"drush/drush": "^12 || ^13"}, "suggest": {"drush/drush": "^12 || ^13"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.3.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/u/graber"}, {"name": "graber", "homepage": "https://www.drupal.org/user/1599440"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}], "description": "Adds an ability to perform bulk operations on selected entities from view results. Provides an API to create such operations.", "homepage": "https://www.drupal.org/project/views_bulk_operations", "support": {"source": "https://git.drupalcode.org/project/views_bulk_operations", "issues": "https://www.drupal.org/project/issues/views_bulk_operations?version=any_4.", "docs": "https://www.drupal.org/docs/contributed-modules/views-bulk-operations-vbo"}}, {"name": "drupal/views_infinite_scroll", "version": "2.0.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/views_infinite_scroll.git", "reference": "2.0.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/views_infinite_scroll-2.0.3.zip", "reference": "2.0.3", "shasum": "3c56969f71256300226118a0f35bad66ab41306c"}, "require": {"drupal/core": "^10.1 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "Honza Pobořil", "homepage": "https://www.drupal.org/user/123612"}, {"name": "neslee canil pinto", "homepage": "https://www.drupal.org/user/3580850"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/143827"}, {"name": "sam152", "homepage": "https://www.drupal.org/user/1485048"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}], "description": "A pager which allows an infinite scroll effect for views.", "homepage": "https://www.drupal.org/project/views_infinite_scroll", "support": {"source": "https://git.drupalcode.org/project/views_infinite_scroll"}}, {"name": "drush/drush", "version": "12.5.3", "source": {"type": "git", "url": "https://github.com/drush-ops/drush.git", "reference": "7fe0a492d5126c457c5fb184c4668a132b0aaac6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drush-ops/drush/zipball/7fe0a492d5126c457c5fb184c4668a132b0aaac6", "reference": "7fe0a492d5126c457c5fb184c4668a132b0aaac6", "shasum": ""}, "require": {"chi-teck/drupal-code-generator": "^3.0", "composer-runtime-api": "^2.2", "composer/semver": "^1.4 || ^3", "consolidation/annotated-command": "^4.9.2", "consolidation/config": "^2.1.2", "consolidation/filter-via-dot-access-data": "^2.0.2", "consolidation/output-formatters": "^4.3.2", "consolidation/robo": "^4.0.6", "consolidation/site-alias": "^4", "consolidation/site-process": "^5.2.0", "ext-dom": "*", "grasmash/yaml-cli": "^3.1", "guzzlehttp/guzzle": "^7.0", "league/container": "^4", "php": ">=8.1", "psy/psysh": "~0.11", "symfony/event-dispatcher": "^6", "symfony/filesystem": "^6.1", "symfony/finder": "^6", "symfony/var-dumper": "^6.0", "symfony/yaml": "^6.0", "webflo/drupal-finder": "^1.2"}, "conflict": {"drupal/core": "< 10.0", "drupal/migrate_run": "*", "drupal/migrate_tools": "<= 5"}, "require-dev": {"composer/installers": "^2", "cweagans/composer-patches": "~1.0", "drupal/core-recommended": "^10", "drupal/semver_example": "2.3.0", "phpunit/phpunit": "^9", "rector/rector": "^0.12", "squizlabs/php_codesniffer": "^3.7"}, "bin": ["drush"], "type": "library", "extra": {"installer-paths": {"sut/core": ["type:drupal-core"], "sut/libraries/{$name}": ["type:drupal-library"], "sut/themes/unish/{$name}": ["drupal/empty_theme"], "sut/drush/contrib/{$name}": ["type:drupal-drush"], "sut/modules/unish/{$name}": ["drupal/devel"], "sut/themes/contrib/{$name}": ["type:drupal-theme"], "sut/modules/contrib/{$name}": ["type:drupal-module"], "sut/profiles/contrib/{$name}": ["type:drupal-profile"]}}, "autoload": {"psr-4": {"Drush\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "j<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Drush is a command line shell and scripting interface for <PERSON><PERSON><PERSON>, a veritable Swiss Army knife designed to make life easier for those of us who spend some of our working hours hacking away at the command prompt.", "homepage": "http://www.drush.org", "support": {"forum": "http://drupal.stackexchange.com/questions/tagged/drush", "issues": "https://github.com/drush-ops/drush/issues", "security": "https://github.com/drush-ops/drush/security/advisories", "slack": "https://drupal.slack.com/messages/C62H9CWQM", "source": "https://github.com/drush-ops/drush/tree/12.5.3"}, "funding": [{"url": "https://github.com/weitzman", "type": "github"}], "time": "2024-08-02T11:57:29+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.21.0", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "5e477468fac5c5ce933dce53af3e8e4e58dcccc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/5e477468fac5c5ce933dce53af3e8e4e58dcccc9", "reference": "5e477468fac5c5ce933dce53af3e8e4e58dcccc9", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.21.0"}, "time": "2025-01-13T09:32:25+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "grasmash/expander", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/grasmash/expander.git", "reference": "eea11b9afb0c32483b18b9009f4ca07b770e39f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/expander/zipball/eea11b9afb0c32483b18b9009f4ca07b770e39f4", "reference": "eea11b9afb0c32483b18b9009f4ca07b770e39f4", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.0", "php": ">=8.0", "psr/log": "^2 | ^3"}, "require-dev": {"greg-1-anderson/composer-test-scenarios": "^1", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\Expander\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Expands internal property references in PHP arrays file.", "support": {"issues": "https://github.com/grasmash/expander/issues", "source": "https://github.com/grasmash/expander/tree/3.0.1"}, "time": "2024-11-25T23:28:05+00:00"}, {"name": "grasmash/yaml-cli", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/grasmash/yaml-cli.git", "reference": "09a8860566958a1576cc54bbe910a03477e54971"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/yaml-cli/zipball/09a8860566958a1576cc54bbe910a03477e54971", "reference": "09a8860566958a1576cc54bbe910a03477e54971", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3", "php": ">=8.0", "symfony/console": "^6 || ^7", "symfony/filesystem": "^6 || ^7", "symfony/yaml": "^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.0"}, "bin": ["bin/yaml-cli"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\YamlCli\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A command line tool for reading and manipulating yaml files.", "support": {"issues": "https://github.com/grasmash/yaml-cli/issues", "source": "https://github.com/grasmash/yaml-cli/tree/3.2.1"}, "time": "2024-04-23T02:10:57+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "league/container", "version": "4.2.5", "source": {"type": "git", "url": "https://github.com/thephpleague/container.git", "reference": "d3cebb0ff4685ff61c749e54b27db49319e2ec00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/container/zipball/d3cebb0ff4685ff61c749e54b27db49319e2ec00", "reference": "d3cebb0ff4685ff61c749e54b27db49319e2ec00", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/container": "^1.1 || ^2.0"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"orno/di": "~2.0"}, "require-dev": {"nette/php-generator": "^3.4", "nikic/php-parser": "^4.10", "phpstan/phpstan": "^0.12.47", "phpunit/phpunit": "^8.5.17", "roave/security-advisories": "dev-latest", "scrutinizer/ocular": "^1.8", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-4.x": "4.x-dev", "dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"League\\Container\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A fast and intuitive dependency injection container.", "homepage": "https://github.com/thephpleague/container", "keywords": ["container", "dependency", "di", "injection", "league", "provider", "service"], "support": {"issues": "https://github.com/thephpleague/container/issues", "source": "https://github.com/thephpleague/container/tree/4.2.5"}, "funding": [{"url": "https://github.com/philipobenito", "type": "github"}], "time": "2025-05-20T12:55:37+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "mck89/peast", "version": "v1.17.2", "source": {"type": "git", "url": "https://github.com/mck89/peast.git", "reference": "465810689c477fbba17f4f949b75e4d0bdab13ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mck89/peast/zipball/465810689c477fbba17f4f949b75e4d0bdab13ea", "reference": "465810689c477fbba17f4f949b75e4d0bdab13ea", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17.2-dev"}}, "autoload": {"psr-4": {"Peast\\": "lib/Peast/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Peast is PHP library that generates AST for JavaScript code", "support": {"issues": "https://github.com/mck89/peast/issues", "source": "https://github.com/mck89/peast/tree/v1.17.2"}, "time": "2025-07-01T09:30:45+00:00"}, {"name": "nikic/php-parser", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/ae59794362fe85e051a58ad36b289443f57be7a9", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.5.0"}, "time": "2025-05-31T08:24:38+00:00"}, {"name": "npm-asset/accessible360--accessible-slick", "version": "1.0.1", "dist": {"type": "tar", "url": "https://registry.npmjs.org/@accessible360/accessible-slick/-/accessible-slick-1.0.1.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/blazy", "version": "1.8.2", "dist": {"type": "tar", "url": "https://registry.npmjs.org/blazy/-/blazy-1.8.2.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/cropper", "version": "4.1.0", "dist": {"type": "tar", "url": "https://registry.npmjs.org/cropper/-/cropper-4.1.0.tgz"}, "require": {"npm-asset/cropperjs": ">=1.5.6,<2.0.0"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "npm-asset/cropperjs", "version": "1.6.2", "dist": {"type": "tar", "url": "https://registry.npmjs.org/cropperjs/-/cropperjs-1.6.2.tgz"}, "type": "npm-asset", "license": ["MIT"]}, {"name": "oomphinc/composer-installers-extender", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/oomphinc/composer-installers-extender.git", "reference": "cbf4b6f9a24153b785d09eee755b995ba87bd5f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/oomphinc/composer-installers-extender/zipball/cbf4b6f9a24153b785d09eee755b995ba87bd5f9", "reference": "cbf4b6f9a24153b785d09eee755b995ba87bd5f9", "shasum": ""}, "require": {"composer-plugin-api": "^1.1 || ^2.0", "composer/installers": "^1.0 || ^2.0", "php": ">=7.1"}, "require-dev": {"composer/composer": "^2.0", "phpunit/phpunit": "^7.2", "squizlabs/php_codesniffer": "^3.3"}, "type": "composer-plugin", "extra": {"class": "OomphInc\\ComposerInstallersExtender\\Plugin"}, "autoload": {"psr-4": {"OomphInc\\ComposerInstallersExtender\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/balbuf"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oomph.is/ndentzau"}], "description": "Extend the composer/installers plugin to accept any arbitrary package type.", "homepage": "http://www.oomphinc.com/", "support": {"issues": "https://github.com/oomphinc/composer-installers-extender/issues", "source": "https://github.com/oomphinc/composer-installers-extender/tree/2.0.1"}, "time": "2021-12-15T12:32:42+00:00"}, {"name": "pear/archive_tar", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/pear/Archive_Tar.git", "reference": "b439c859564f5cbb0f64ad6002d0afe84a889602"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Archive_Tar/zipball/b439c859564f5cbb0f64ad6002d0afe84a889602", "reference": "b439c859564f5cbb0f64ad6002d0afe84a889602", "shasum": ""}, "require": {"pear/pear-core-minimal": "^1.10.0alpha2", "php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-bz2": "Bz2 compression support.", "ext-xz": "Lzma2 compression support.", "ext-zlib": "Gzip compression support."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Archive_Tar": ""}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Tar file management class with compression support (gzip, bzip2, lzma2)", "homepage": "https://github.com/pear/Archive_Tar", "keywords": ["archive", "tar"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Archive_Tar", "source": "https://github.com/pear/Archive_Tar"}, "time": "2024-03-16T16:21:40+00:00"}, {"name": "pear/console_getopt", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/pear/Console_Getopt.git", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Console_Getopt/zipball/a41f8d3e668987609178c7c4a9fe48fecac53fa0", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Console": "./"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "Stig Bakken", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Helper"}], "description": "More info available on: http://pear.php.net/package/Console_<PERSON>opt", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Console_Getopt", "source": "https://github.com/pear/Console_<PERSON>opt"}, "time": "2019-11-20T18:27:48+00:00"}, {"name": "pear/pear-core-minimal", "version": "v1.10.16", "source": {"type": "git", "url": "https://github.com/pear/pear-core-minimal.git", "reference": "c0f51b45f50683bf5bbf558036854ebc9b54d033"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/pear-core-minimal/zipball/c0f51b45f50683bf5bbf558036854ebc9b54d033", "reference": "c0f51b45f50683bf5bbf558036854ebc9b54d033", "shasum": ""}, "require": {"pear/console_getopt": "~1.4", "pear/pear_exception": "~1.0", "php": ">=5.4"}, "replace": {"rsky/pear-core-min": "self.version"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["src/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "Minimal set of PEAR core files to be used as composer dependency", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR", "source": "https://github.com/pear/pear-core-minimal"}, "time": "2024-11-24T22:27:58+00:00"}, {"name": "pear/pear_exception", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/pear/PEAR_Exception.git", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/PEAR_Exception/zipball/b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "<9"}, "type": "class", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["PEAR/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["."], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PEAR Exception base class.", "homepage": "https://github.com/pear/PEAR_Exception", "keywords": ["exception"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR_Exception", "source": "https://github.com/pear/PEAR_Exception"}, "time": "2021-03-21T15:43:46+00:00"}, {"name": "phootwork/collection", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/phootwork/collection.git", "reference": "46dde20420fba17766c89200bc3ff91d3e58eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phootwork/collection/zipball/46dde20420fba17766c89200bc3ff91d3e58eafa", "reference": "46dde20420fba17766c89200bc3ff91d3e58eafa", "shasum": ""}, "require": {"phootwork/lang": "^3.0", "php": ">=8.0"}, "type": "library", "autoload": {"psr-4": {"phootwork\\collection\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "The phootwork library fills gaps in the php language and provides better solutions than the existing ones php offers.", "homepage": "https://phootwork.github.io/collection/", "keywords": ["Array object", "Text object", "collection", "collections", "json", "list", "map", "queue", "set", "stack", "xml"], "support": {"issues": "https://github.com/phootwork/phootwork/issues", "source": "https://github.com/phootwork/collection/tree/v3.2.3"}, "time": "2022-08-27T12:51:24+00:00"}, {"name": "phootwork/lang", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/phootwork/lang.git", "reference": "52ec8cce740ce1c424eef02f43b43d5ddfec7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phootwork/lang/zipball/52ec8cce740ce1c424eef02f43b43d5ddfec7b5e", "reference": "52ec8cce740ce1c424eef02f43b43d5ddfec7b5e", "shasum": ""}, "require": {"php": ">=8.0", "symfony/polyfill-mbstring": "^1.12", "symfony/polyfill-php81": "^1.22"}, "type": "library", "autoload": {"psr-4": {"phootwork\\lang\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "Missing PHP language constructs", "homepage": "https://phootwork.github.io/lang/", "keywords": ["array", "comparator", "comparison", "string"], "support": {"issues": "https://github.com/phootwork/phootwork/issues", "source": "https://github.com/phootwork/lang/tree/v3.2.3"}, "time": "2024-10-03T13:43:19+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "phpowermove/docblock", "version": "v4.0", "source": {"type": "git", "url": "https://github.com/phpowermove/docblock.git", "reference": "a73f6e17b7d4e1b92ca5378c248c952c9fae7826"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpowermove/docblock/zipball/a73f6e17b7d4e1b92ca5378c248c952c9fae7826", "reference": "a73f6e17b7d4e1b92ca5378c248c952c9fae7826", "shasum": ""}, "require": {"phootwork/collection": "^3.0", "phootwork/lang": "^3.0", "php": ">=8.0"}, "require-dev": {"phootwork/php-cs-fixer-config": "^0.4", "phpunit/phpunit": "^9.0", "psalm/phar": "^4.3"}, "type": "library", "autoload": {"psr-4": {"phpowermove\\docblock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "PHP Docblock parser and generator. An API to read and write Docblocks.", "keywords": ["doc<PERSON>", "generator", "parser"], "support": {"issues": "https://github.com/phpowermove/docblock/issues", "source": "https://github.com/phpowermove/docblock/tree/v4.0"}, "time": "2021-09-22T16:57:06+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psy/psysh", "version": "v0.12.9", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "1b801844becfe648985372cb4b12ad6840245ace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/1b801844becfe648985372cb4b12ad6840245ace", "reference": "1b801844becfe648985372cb4b12ad6840245ace", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.9"}, "time": "2025-06-23T02:35:06+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "scssphp/scssphp", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/scssphp/scssphp.git", "reference": "63d1157457e5554edf00b0c1fabab4c1511d2520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/scssphp/scssphp/zipball/63d1157457e5554edf00b0c1fabab4c1511d2520", "reference": "63d1157457e5554edf00b0c1fabab4c1511d2520", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "php": ">=5.6.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4", "phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.3 || ^9.4", "sass/sass-spec": "*", "squizlabs/php_codesniffer": "~3.5", "symfony/phpunit-bridge": "^5.1", "thoughtbot/bourbon": "^7.0", "twbs/bootstrap": "~5.0", "twbs/bootstrap4": "4.6.1", "zurb/foundation": "~6.7.0"}, "suggest": {"ext-iconv": "Can be used as fallback when ext-mbstring is not available", "ext-mbstring": "For best performance, mbstring should be installed as it is faster than ext-iconv"}, "bin": ["bin/pscss"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}}, "autoload": {"psr-4": {"ScssPhp\\ScssPhp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/robocoder"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Cerdic"}], "description": "scssphp is a compiler for SCSS written in PHP.", "homepage": "http://scssphp.github.io/scssphp/", "keywords": ["css", "less", "sass", "scss", "stylesheet"], "support": {"issues": "https://github.com/scssphp/scssphp/issues", "source": "https://github.com/scssphp/scssphp/tree/v1.13.0"}, "time": "2024-08-17T21:02:11+00:00"}, {"name": "sebastian/diff", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:30:58+00:00"}, {"name": "symfony/console", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "9056771b8eca08d026cd3280deeec3cfd99c4d93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/9056771b8eca08d026cd3280deeec3cfd99c4d93", "reference": "9056771b8eca08d026cd3280deeec3cfd99c4d93", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:37:22+00:00"}, {"name": "symfony/dependency-injection", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "0d9f24f3de0a83573fce5c9ed025d6306c6e166b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/0d9f24f3de0a83573fce5c9ed025d6306c6e166b", "reference": "0d9f24f3de0a83573fce5c9ed025d6306c6e166b", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.1", "symfony/finder": "<5.4", "symfony/proxy-manager-bridge": "<6.3", "symfony/yaml": "<5.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.1|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-23T06:49:06+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/error-handler", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "b088e0b175c30b4e06d8085200fa465b586f44fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/b088e0b175c30b4e06d8085200fa465b586f44fa", "reference": "b088e0b175c30b4e06d8085200fa465b586f44fa", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-13T07:39:48+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:07:50+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T13:51:37+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "452d19f945ee41345fd8a50c18b60783546b7bd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/452d19f945ee41345fd8a50c18b60783546b7bd3", "reference": "452d19f945ee41345fd8a50c18b60783546b7bd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-26T09:17:58+00:00"}, {"name": "symfony/http-kernel", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "2bb2cba685aabd859f22cf6946554e8e7f3c329a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/2bb2cba685aabd859f22cf6946554e8e7f3c329a", "reference": "2bb2cba685aabd859f22cf6946554e8e7f3c329a", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<6.1", "symfony/console": "<5.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<5.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.3", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/config": "^6.1|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.5|^6.0.5|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.4|^7.0.4", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.4|^7.0", "symfony/var-exporter": "^6.2|^7.0", "twig/twig": "^2.13|^3.0.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-28T08:14:51+00:00"}, {"name": "symfony/mailer", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "a480322ddf8e54de262c9bca31fdcbe26b553de5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/a480322ddf8e54de262c9bca31fdcbe26b553de5", "reference": "a480322ddf8e54de262c9bca31fdcbe26b553de5", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-26T21:24:02+00:00"}, {"name": "symfony/mime", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/fec8aa5231f3904754955fad33c2db50594d22d1", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:27:38+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/48becf00c920479ca2e910c22a5a39e5d47ca956", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/e2a61c16af36c9a07e5c9906498b73e091949a20", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-10T17:11:00+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "c9cf83326a1074f83a738fc5320945abf7fb7fec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/c9cf83326a1074f83a738fc5320945abf7fb7fec", "reference": "c9cf83326a1074f83a738fc5320945abf7fb7fec", "shasum": ""}, "require": {"php": ">=8.1", "psr/http-message": "^1.0|^2.0", "symfony/http-foundation": "^5.4|^6.0|^7.0"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-kernel": "<6.2"}, "require-dev": {"nyholm/psr7": "^1.1", "php-http/discovery": "^1.15", "psr/log": "^1.1.4|^2|^3", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/framework-bundle": "^6.2|^7.0", "symfony/http-kernel": "^6.2|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "https://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/routing", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/1f5234e8457164a3a0038a4c0a4ba27876a9c670", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T16:08:38+00:00"}, {"name": "symfony/serializer", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "b40a697a2bb2c3d841a1f9e34a8a9f50bf9d1d06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/b40a697a2bb2c3d841a1f9e34a8a9f50bf9d1d06", "reference": "b40a697a2bb2c3d841a1f9e34a8a9f50bf9d1d06", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<5.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4.24|>=6,<6.2.11", "symfony/uid": "<5.4", "symfony/validator": "<6.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.26|^6.3|^7.0", "symfony/property-info": "^5.4.24|^6.2.11|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T15:34:20+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "73e2c6966a5aef1d4892873ed5322245295370c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/73e2c6966a5aef1d4892873ed5322245295370c6", "reference": "73e2c6966a5aef1d4892873ed5322245295370c6", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-18T15:23:29+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/validator", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "6506760ab57e7cda5bde9cdaed736526162284bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/6506760ab57e7cda5bde9cdaed736526162284bc", "reference": "6506760ab57e7cda5bde9cdaed736526162284bc", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3|^7.0.3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-26T07:25:45+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "d55b1834cdbfcc31bc2cd7e095ba5ed9a88f6600"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/d55b1834cdbfcc31bc2cd7e095ba5ed9a88f6600", "reference": "d55b1834cdbfcc31bc2cd7e095ba5ed9a88f6600", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T15:05:27+00:00"}, {"name": "symfony/var-exporter", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/f28cf841f5654955c9f88ceaf4b9dc29571988a9", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-14T13:00:13+00:00"}, {"name": "symfony/yaml", "version": "v6.4.23", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "93e29e0deb5f1b2e360adfb389a20d25eb81a27b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/93e29e0deb5f1b2e360adfb389a20d25eb81a27b", "reference": "93e29e0deb5f1b2e360adfb389a20d25eb81a27b", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-03T06:46:12+00:00"}, {"name": "twig/twig", "version": "v3.20.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "3468920399451a384bef53cf7996965f7cd40183"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/3468920399451a384bef53cf7996965f7cd40183", "reference": "3468920399451a384bef53cf7996965f7cd40183", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.20.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-02-13T08:34:43+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.2", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2025-04-30T23:37:27+00:00"}, {"name": "webflo/drupal-finder", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/webflo/drupal-finder.git", "reference": "73045060b0894c77962a10cff047f72872d8810c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webflo/drupal-finder/zipball/73045060b0894c77962a10cff047f72872d8810c", "reference": "73045060b0894c77962a10cff047f72872d8810c", "shasum": ""}, "require": {"composer-runtime-api": "^2.2", "php": ">=8.1"}, "require-dev": {"mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^10.4", "symfony/process": "^6.4"}, "type": "library", "autoload": {"psr-4": {"DrupalFinder\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Helper class to locate a <PERSON><PERSON><PERSON> installation.", "support": {"issues": "https://github.com/webflo/drupal-finder/issues", "source": "https://github.com/webflo/drupal-finder/tree/1.3.1"}, "time": "2024-06-28T13:45:36+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "webmozart/path-util", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/webmozart/path-util.git", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/path-util/zipball/d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "shasum": ""}, "require": {"php": ">=5.3.3", "webmozart/assert": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\PathUtil\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A robust cross-platform utility for normalizing, comparing and modifying file paths.", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.3.0"}, "abandoned": "symfony/filesystem", "time": "2015-12-17T08:42:14+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}