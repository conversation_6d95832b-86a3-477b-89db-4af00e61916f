label: Media
display:
  default:
    display_title: Master
    display_options:
      exposed_form:
        options:
          submit_button: Filter
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        options:
          expose:
            items_per_page_label: 'Items per page'
            items_per_page_options_all_label: '- All -'
            offset_label: Offset
          tags:
            previous: '‹ Previous'
            next: 'Next ›'
            first: '« First'
            last: 'Last »'
      fields:
        media_bulk_form:
          action_title: Action
        thumbnail__target_id:
          label: Thumbnail
          separator: ', '
        name:
          label: 'Media name'
          separator: ', '
        bundle:
          label: Type
          separator: ', '
        langcode:
          label: Language
          separator: ', '
        status:
          label: Status
          settings:
            format_custom_true: Published
            format_custom_false: Unpublished
          separator: ', '
        uid:
          label: Author
          separator: ', '
        changed:
          label: Updated
          separator: ', '
        operations:
          label: Operations
      filters:
        name:
          expose:
            label: 'Media name'
        bundle:
          expose:
            label: Type
        status:
          expose:
            label: 'True'
          group_info:
            label: 'Published status'
            group_items:
              1:
                title: Published
              2:
                title: Unpublished
        langcode:
          expose:
            label: Language
      title: Media
      empty:
        area_text_custom:
          content: 'No media available.'
  media_page_list:
    display_title: Media
    display_options:
      menu:
        title: Media
