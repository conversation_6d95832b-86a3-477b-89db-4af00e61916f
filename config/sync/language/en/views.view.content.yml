label: Content
description: 'Find and manage content.'
display:
  default:
    display_options:
      exposed_form:
        options:
          submit_button: Filter
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        options:
          tags:
            previous: '‹ Previous'
            next: 'Next ›'
            first: '« First'
            last: 'Last »'
      fields:
        node_bulk_form:
          action_title: Action
        title:
          label: Title
        type:
          label: 'Content type'
          separator: ', '
        ds_switch:
          label: Display
          separator: ', '
        name:
          label: Author
        status:
          label: Status
          settings:
            format_custom_true: Published
            format_custom_false: Unpublished
        changed:
          label: Updated
        langcode_1:
          label: 'Original language'
          separator: ', '
        operations:
          label: Operations
        field_detall_grup:
          label: Tipus
          separator: ', '
      filters:
        title:
          expose:
            label: Title
        type:
          expose:
            label: 'Content type'
        field_detall_grup_target_id:
          expose:
            label: 'Grup (field_detall_grup)'
        status:
          expose:
            label: Status
          group_info:
            label: 'Published status'
            group_items:
              1:
                title: Published
              2:
                title: Unpublished
        langcode:
          expose:
            label: Language
      title: Content
      empty:
        area_text_custom:
          content: 'No content available.'
    display_title: Master
  page_1:
    display_options:
      menu:
        title: Content
      tab_options:
        title: Content
        description: 'Find and manage content'
    display_title: Page
