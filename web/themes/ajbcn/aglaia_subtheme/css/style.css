@import url("https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap");
:root {
  --font-xl: 78px;
  --font-l: 3rem;
  --font-m-et-plus: 2rem;
  --font-m: 1.1904789rem;
  --font-s: 1rem;
  --gap: 1.3rem;
  --back-modal: #e5e5e5;
  --back-corpo: #D93D00;
  --button-action: calc(var(--header-top) * 0.6 - 2px);
  --header-top: 20vw;
  --filter-base: 10vh;
  --page-margin: 4vw;
  --page-horizontal-margin: 8vw;
  --deep-grey: #222;
}
@media (min-width: 1024px) {
  :root {
    --header-top: 6vw;
    --gap: 2rem;
  }
}

* {
  box-sizing: border-box;
}

body,
html {
  margin: 0;
  font-size: 3vw !important;
}
@media (min-width: 1024px) {
  body,
  html {
    font-size: 0.85vw !important;
  }
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: "Source Sans 3", sans-serif !important;
  font-optical-sizing: auto;
  color: var(--deep-grey) !important;
  font-weight: 300;
  font-style: normal;
  transition: background-color 0.6s;
}
body.oberta {
  background-color: #555;
}
body.mapa-loaded #loading-map {
  opacity: 0;
  -webkit-backdrop-filter: blur(0px);
          backdrop-filter: blur(0px);
}
body #loading-map {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  z-index: 1;
  font-size: 0;
  opacity: 1;
  transition: opacity 1s ease-in-out, -webkit-backdrop-filter 1s ease-in-out;
  transition: opacity 1s ease-in-out, backdrop-filter 1s ease-in-out;
  transition: opacity 1s ease-in-out, backdrop-filter 1s ease-in-out, -webkit-backdrop-filter 1s ease-in-out;
  overflow: hidden;
  pointer-events: none;
  text-indent: -1000vw;
}
body #loading-map:after {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  display: block;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border-radius: 50%;
  border: 4px solid #ccc;
  border-top-color: var(--back-corpo);
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

aside,
nav,
footer {
  width: 20%;
  position: relative;
  z-index: 1;
}

main {
  padding-left: calc(var(--page-horizontal-margin) * 0.2);
  padding-right: calc(var(--page-horizontal-margin) * 0.2);
  min-height: 100vh;
  background: var(--back-modal);
}
@media (min-width: 1024px) {
  main {
    padding-left: calc(var(--page-horizontal-margin) * 3);
    padding-right: calc(var(--page-horizontal-margin) * 3);
  }
}

#vue > header,
#no-vue > header {
  position: fixed;
  top: 1vh;
  left: 0;
  border-top-right-radius: calc((var(--button-action) + 1.2rem) * 0.5);
  border-bottom-right-radius: calc((var(--button-action) + 1.2rem) * 0.5);
  align-items: center;
  z-index: 100;
  display: grid;
  gap: calc(var(--gap) * 0.5);
  height: var(--button-action);
  width: 83vw;
  padding: 0.7rem;
  padding-left: 4vw;
  padding-top: 0.1em;
  padding-bottom: 0.1em;
  background: rgb(255, 255, 255);
  -webkit-backdrop-filter: blur(3px);
          backdrop-filter: blur(3px);
}
@media (min-width: 1024px) {
  #vue > header,
  #no-vue > header {
    width: auto;
    height: auto;
    padding: 0.7rem;
    padding-right: 1em;
    top: calc(var(--header-top) * 0.25 - 0.2rem);
    left: calc(var(--header-top) * 0.25 + 1rem);
    border-top-right-radius: calc((var(--button-action) + 1.2rem) * 0.5);
    border-bottom-right-radius: calc((var(--button-action) + 1.2rem) * 0.5);
  }
}
#vue > header h1,
#no-vue > header h1 {
  padding: 0;
  padding-right: var(--gap);
  border-right: 1px solid var(--deep-grey);
  width: -moz-min-content;
  width: min-content;
  z-index: 9;
  font-size: var(--font-m);
  font-weight: 900;
  color: var(--deep-grey);
  line-height: 0.95;
  letter-spacing: -0.06em;
  text-transform: uppercase;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
  margin: 0;
  transition: all 0.6s;
  color: var(--back-corpo);
}
#vue > header h1 span,
#no-vue > header h1 span {
  font-weight: 300;
  display: none;
}
#vue > header > p,
#no-vue > header > p {
  display: none;
}
#vue > header .resultat-cerca,
#no-vue > header .resultat-cerca {
  grid-column: 3;
  border-left: 1px solid;
  width: 9.25em;
  overflow: hidden;
  height: 100%;
  padding-left: calc(var(--gap) * 0.5);
  padding-right: 0;
  display: flex;
  font-weight: 400;
  align-items: center;
  font-size: var(--font-m);
  opacity: 0;
  transition: all 0.6s;
}
@media (min-width: 1024px) {
  #vue > header .resultat-cerca,
  #no-vue > header .resultat-cerca {
    padding-left: var(--gap);
    width: 10em;
  }
}
#vue > header .filtres-button,
#no-vue > header .filtres-button {
  background-color: var(--back-corpo);
  aspect-ratio: 1;
  color: white;
  top: calc(var(--header-top) * 0.25 + 1rem);
  grid-column: 11;
  width: var(--button-action);
  border-radius: 50%;
  position: fixed;
  right: 10vw;
  z-index: 9;
}
#vue > header .titol-pagina,
#no-vue > header .titol-pagina {
  display: block;
  text-transform: uppercase;
  color: var(--deep-grey);
  z-index: 9;
  grid-column: 2;
  grid-row: 1;
  font-weight: 900;
  font-size: var(--font-m);
  margin: 0;
  line-height: 0.95;
  letter-spacing: -0.06em;
  width: 3.7em;
}

.menu-button {
  background-color: white;
  aspect-ratio: 1;
  color: white;
  top: 1vh;
  grid-column: 12;
  width: var(--button-action);
  border-radius: 50%;
  position: fixed;
  right: 3vw;
  z-index: 100;
}
@media (min-width: 1024px) {
  .menu-button {
    top: calc(var(--header-top) * 0.25 + 0.5rem + 1px);
    z-index: 9;
  }
}
.menu-button:hover {
  cursor: pointer;
  background-color: var(--back-corpo);
}
.menu-button:hover span {
  background: white;
}
.menu-button:hover span:before, .menu-button:hover span:after {
  background: white;
}
.menu-button span {
  width: 33%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: black;
  display: block;
  height: 3px;
}
.menu-button span:before, .menu-button span:after {
  content: "";
  display: block;
  width: 100%;
  height: 3px;
  background: black;
  position: absolute;
  top: 0.6em;
}
.menu-button span:before {
  top: -0.6em;
}

.sr-only {
  display: none;
}

.content-map {
  perspective: 500px;
  width: 100%;
  height: 100vh;
  position: fixed;
  z-index: 0;
  top: 0;
  right: 0;
  background: #222;
}
.content-map #map {
  width: 100%;
  height: 100vh;
  position: fixed;
  z-index: -1;
  top: 0;
  right: 0;
  background: #222;
  transition: all 0.6s;
}

body.light .content-map {
  background: #eee;
}
body.light .content-map #map,
body.light .content-map #map_fitxa {
  background: #eee;
}
body.light .clar {
  font-weight: 700;
  letter-spacing: -0.006em;
}
body.light .fosc {
  font-weight: 400;
  letter-spacing: 0;
}
body.light .modal-fitxa {
  box-shadow: rgba(0, 0, 0, 0.2) 0vw 0vh 20vw 3vw;
}
body.light .modal-fitxa .modal-content header .fitxa-titol,
body.light .modal-fitxa .modal-content header .codi {
  color: var(--back-corpo);
}
body.light .modal-fitxa .close-modal-button,
body.light .modal-fitxa .maxi-modal-button {
  background-color: var(deep-grey);
  color: white;
}
body.light .llista .llista-content {
  background: var(--back-modal);
  color: var(--deep-grey);
}
body.light .llista .llista-content li {
  border-bottom: 1px solid #d5d5d5;
}
body.light .llista .llista-content li:hover {
  background: rgba(255, 255, 255, 0.3);
}
body.light .llista .llista-content li.header {
  background: var(--back-modal);
}

.color-map {
  position: fixed;
  border-bottom-left-radius: 0.8rem;
  border-bottom-right-radius: 0.8rem;
  background: var(--back-corpo);
  color: white;
  top: 100%;
  left: 4vw;
  text-align: center;
  width: -moz-max-content;
  width: max-content;
  padding: calc(var(--gap) * 0.3);
  padding-left: calc(var(--gap) * 0.7);
  padding-right: calc(var(--gap) * 0.7);
  display: flex;
  text-transform: uppercase;
  font-size: 0.8em;
  font-weight: 400;
  gap: calc(var(--gap) * 0.3);
  justify-content: center;
  align-items: center;
}
@media (min-width: 1024px) {
  .color-map {
    transform: translate(-50%);
    left: 50%;
  }
}
.color-map .clar,
.color-map .fosc {
  cursor: pointer;
  transition: all 0.6s;
}
.color-map .fosc {
  font-weight: 700;
  letter-spacing: -0.006em;
}
.color-map .switch {
  position: relative;
  display: inline-block;
  width: 3.5rem;
  height: 1.4rem;
}
.color-map .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.color-map .switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--deep-grey);
  transition: 0.4s;
}
.color-map .switch .slider:before {
  position: absolute;
  content: "";
  height: 0.9rem;
  width: 0.9rem;
  left: 0.3rem;
  bottom: 0.3rem;
  background-color: var(--back-corpo);
  transition: 0.4s;
}
.color-map .switch input:checked + .slider {
  background-color: #ccc;
}
.color-map .switch input:checked + .slider:before {
  transform: translateX(2rem);
}
.color-map .switch .slider.round {
  border-radius: 34px;
}
.color-map .switch .slider.round:before {
  border-radius: 50%;
}

.mapboxgl-popup-content {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap) * 0.4);
  padding: 1em !important;
  border: solid 2px var(--back-corpo);
  background: rgba(51, 51, 51, 0.8) !important;
  border-radius: 4px;
  -webkit-backdrop-filter: blur(3px);
          backdrop-filter: blur(3px);
}
.mapboxgl-popup-content button,
.mapboxgl-popup-content [type=button],
.mapboxgl-popup-content [type=reset],
.mapboxgl-popup-content [type=submit] {
  border: 0;
  padding: 0;
  background: transparent;
  color: white;
  font-size: calc(var(--font-s) * 0.9);
  font-weight: 300;
  font-family: "Source Sans 3", sans-serif;
  line-height: 1.1;
  text-align: unset;
}
.mapboxgl-popup-content .mapboxgl-popup-close-button {
  display: none;
  padding: 2px;
  width: 1.7em;
  transform: translate(2px, -2px);
  line-height: 1;
  color: white;
  aspect-ratio: 1;
  text-transform: uppercase;
  border-bottom-left-radius: 3px;
}
.mapboxgl-popup-content .mapboxgl-popup-close-button:hover {
  color: white;
  background: var(--back-corpo);
}

.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
  border-top-color: var(--back-corpo) !important;
}

.vertical {
  transform: perspective(500px) rotateY(-0.5deg) rotateX(0deg) translateZ(0px);
  transform-origin: 100% 50%;
  opacity: 0.5;
  filter: blur(3px);
}

.horizontal {
  transform: perspective(500px) rotateY(0deg) rotateX(0.5deg) translateZ(0px);
  transform-origin: 50% 100%;
  opacity: 0.5;
  filter: blur(3px);
}

#map_fitxa {
  width: calc(100% - var(--gap) * 0.5);
  height: 50vh;
  position: absolute;
  top: calc(var(--gap) * 0.25);
  left: calc(var(--gap) * 0.25);
  z-index: 0;
  background: #222;
  transition: all 0.6s;
  pointer-events: none;
}

.maxi-menu-button {
  display: none;
}

#menu-nav {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 101;
  right: 0;
  top: 0vh;
  transform: translate(100%, 0%);
  opacity: 0;
  background: white;
  transition: all 0.6s;
  padding: 2em;
  padding-top: 0;
}
@media (min-width: 1024px) {
  #menu-nav {
    width: 33vw;
  }
}
#menu-nav .close-menu-button {
  position: absolute;
  width: 3em;
  border-radius: 50%;
  background: black;
  text-indent: -100vw;
  overflow: hidden;
  aspect-ratio: 1;
  background-image: url(../img/close_b.svg);
  background-position: center;
  background-size: 40%;
  background-repeat: no-repeat;
}
#menu-nav .close-menu-button:hover {
  cursor: pointer;
  background-color: var(--back-corpo);
}
#menu-nav #menu-nav .close-menu-button:hover {
  color: white;
}
#menu-nav.active {
  transform: translate(0%, 0%);
  opacity: 1;
  box-shadow: -12vw 0 24vw rgba(0, 0, 0, 0.3);
}
#menu-nav .links {
  padding: 0;
  margin: 0;
  list-style: none;
  display: grid;
  grid-template-rows: 30% auto 1fr auto auto;
  height: 90vh;
  font-weight: 600;
  font-size: calc(var(--font-m) * 1);
}
#menu-nav .links a {
  color: var(--black);
}
#menu-nav .links li:nth-child(n+2):nth-child(-n+3) {
  font-size: var(--font-l);
  text-transform: uppercase;
}

.cercador-button {
  display: none;
  background-color: var(--deep-grey);
  color: white;
  background-image: url(../img/icon-search-w.svg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 33%;
  transform: translateY(-50%);
  width: var(--button-action);
  height: var(--button-action);
  position: fixed;
  z-index: 12;
  left: 4px;
  top: 40vh;
}
@media (min-width: 1024px) {
  .cercador-button {
    top: calc(var(--header-top) * 0.25 + 1rem + var(--gap) + var(--font-m) * 2 * 0.95);
    left: auto;
    right: 22vw;
  }
}

.cercador {
  display: grid;
  grid-template-columns: auto auto;
  gap: calc(var(--gap) * 3);
  transform: translateY(-50%);
  height: var(--button-action);
  border-radius: 0px;
  position: fixed;
  left: 4vw;
  top: 35vh;
  transition: all 0.6s;
  padding-left: 0.1vh;
}
@media (min-width: 1024px) {
  .cercador {
    left: calc(100% + var(--button-action) - 2px + var(--gap) * 6);
    top: calc(var(--header-top) * 0.25 + 1.2rem + 1px);
  }
}
.cercador input.lupa {
  width: var(--button-action);
  height: calc(var(--button-action) - 2px);
  border: 0;
  grid-column: 3;
  aspect-ratio: 1;
  background-color: var(--back-corpo);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.5s;
  background-image: url(../img/icon-search-w.svg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 30%;
}
.cercador input.text-cerca {
  grid-column: 2;
  overflow: hidden;
  height: calc(var(--button-action) - 2px);
  border: 0;
  background-color: white;
  font-size: 1em;
  left: 0;
  top: 0;
  opacity: 0;
  margin: 0;
  width: 0;
  padding: 0;
  padding-top: 1em;
  padding-bottom: 1em;
  transition: all 0.5s;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.cercador input.text-cerca::-webkit-input-placeholder {
  color: black;
  font-size: 0.9em;
  text-transform: uppercase;
  font-weight: 700;
}
.cercador .resultat-cerca {
  grid-column: 1;
  grid-row: 1;
  font-size: var(--font-m);
  font-weight: 700;
  height: 100%;
  background: white;
  display: flex;
}
.cercador .resultat-cerca .filtrar-button {
  cursor: pointer;
}
.cercador .resultat-cerca .filtrar-button:hover {
  text-decoration: underline;
}
.cercador .resultat-cerca .filtrar-button.obert:after {
  content: " x";
}
.cercador.active {
  z-index: 10;
  color: black;
}
.cercador.active input.lupa,
.cercador.active input.text-cerca {
  opacity: 1;
}
.cercador.active .cercador-content {
  display: grid;
  align-items: start;
  height: 100%;
}
.cercador.active .cercador-content:hover input.lupa {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.cercador.active .cercador-content:hover input.text-cerca {
  width: 17.2rem;
  padding: 1em;
  border-top-left-radius: calc(var(--button-action) * 0.5);
  border-bottom-left-radius: calc(var(--button-action) * 0.5);
}
.cercador #close-cerca {
  display: none;
}

.llista-button {
  cursor: pointer;
  background: var(--back-corpo);
  color: transparent;
  height: calc(var(--button-action) - 2px);
  top: calc(var(--header-top) * 0.25 + 1rem);
  width: calc(var(--button-action) - 2px);
  border-radius: 0px;
  padding: 0.5em;
  grid-column: 4;
  right: 10vw;
  display: flex;
  z-index: 9;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1.1;
  font-size: 0.8em;
  font-weight: 400;
  text-transform: uppercase;
  transition: all 0.6s;
  border-radius: 50%;
  position: fixed;
  left: -77vw;
  top: 14vh;
}
@media (min-width: 1024px) {
  .llista-button {
    position: static;
  }
}
.llista-button:hover {
  background: var(--back-corpo);
}
.llista-button a {
  color: white;
}

.filtres {
  position: fixed;
  top: calc((4vh + 12rem) * 0.5 - var(--button-action) * 0.5);
  right: 16vw;
  width: var(--button-action);
  height: var(--button-action);
  border-radius: calc(var(--button-action) * 0.5);
  overflow: hidden;
  background: var(--back-modal);
  box-shadow: 0 6vh 20vh rgba(0, 0, 0, 0.5);
  transition: all 0.6s;
  z-index: 9;
  display: none;
}
.filtres.oberta {
  width: calc(80vw - 12rem - 14em);
  transition: all 0.6s;
}
.filtres.maxi {
  top: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  border-radius: 0;
  opacity: 1;
  background: var(--back-modal);
}
.filtres.maxi .maxi-filtres-button {
  background-image: url(../img/fs_m_b.svg);
}
.filtres.maxi .filter-box {
  opacity: 1;
}

.llista {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  border-radius: 0;
  opacity: 0;
  background: var(--back-modal);
  transition: all 0.6s;
  z-index: -1;
}
.llista .llista-content {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding-left: var(--page-horizontal-margin);
  padding-right: var(--page-horizontal-margin);
  width: 100%;
  overflow: hidden;
  padding-top: calc(4vh + 12rem);
  margin-top: 0;
  background: var(--deep-grey);
  color: white;
  transition: all 0.4s;
}

.llista.maxi {
  opacity: 1 !important;
  z-index: 7;
}

.llista .result-list {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-bottom: calc(4vh + 12rem);
  width: 100%;
  height: 100%;
  overflow: auto;
}
.llista .result-list > p {
  display: none;
}
.llista .result-list li {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  border: 1px solid transparent;
  border-bottom: 1px solid #333;
  padding: 1em;
  padding-left: 0;
  padding-right: 0;
  margin: 0;
  transition: all 0.4s;
}
.llista .result-list li p {
  margin: 0;
}
.llista .result-list li .identifier {
  color: var(--back-corpo);
  display: flex;
  justify-content: space-between;
}
.llista .result-list li .identifier span {
  font-weight: 700;
  flex-basis: 70%;
}
.llista .result-list li .districts {
  text-align: end;
}
.llista .result-list li:hover {
  cursor: pointer;
  background: rgba(0, 0, 0, 0.3);
  padding-left: 1em;
  padding-right: 1em;
}
.llista .result-list li.header {
  font-weight: 900;
  position: sticky;
  top: 0;
  background: var(--deep-grey);
}
.llista .result-list li.header:hover {
  background-color: inherit;
  padding-left: 0;
  padding-right: 0;
}
.llista .view-more button {
  all: unset;
  background-color: var(--back-corpo);
  width: calc(var(--button-action) - 2px);
  aspect-ratio: 1;
  text-indent: -100vw;
  background-image: url(../img/more_b.svg);
  background-size: 30%;
  margin: auto;
  margin-top: calc(var(--gap) * 2);
  background-repeat: no-repeat;
  background-position: center center;
  overflow: hidden;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.9em;
  font-weight: 400;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.6s;
  border: 0;
  border-radius: 100%;
  box-sizing: border-box;
}

.navegacio {
  display: flex;
  list-style: none;
  gap: var(--gap);
  justify-content: center;
  margin-top: 3em;
}
.navegacio .actiu {
  font-weight: 700;
}
.navegacio a {
  color: black;
  text-decoration: none;
}
.navegacio a:hover {
  text-decoration: underline;
  font-weight: 600;
}

.filtres .filtres-content {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding-top: calc(4vh + 12rem);
  padding-left: calc(12rem + 4vw);
  width: 100%;
}
.filtres .filter-box {
  height: calc(100% - var(--button-action) - var(--gap) * 4);
  opacity: 0;
  transition: all 0.6s 0.6s;
}
.filtres .filter-box form {
  height: 100%;
}
.filtres .filter-box .filter-list {
  display: flex;
  gap: calc(var(--gap) * 2);
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
}
.filtres .filter-box .filter-list > li {
  height: 100%;
  overflow: auto;
}
.filtres .filter-box .filter-list > li .filter-type-title {
  font-weight: 600;
}
.filtres .filter-box .filter-list > li .sub-list ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.filtres .filter-box .filter-list > li .sub-list ul li input {
  width: 1px;
  height: 1px;
  opacity: 0.0001;
  border: 0;
  position: absolute;
}
.filtres .filter-box .filter-list > li .sub-list ul li input[disabled] + label {
  opacity: 0.3;
}
.filtres .filter-box .filter-list > li .sub-list ul li input[type=checkbox]:checked:disabled + label {
  opacity: 0.7;
}
.filtres .filter-box .filter-list > li .sub-list ul li input[type=checkbox]:checked + label {
  opacity: 1;
  font-weight: 600;
}
.filtres .filter-box .filter-base {
  display: flex;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  align-items: center;
  justify-content: space-evenly;
  padding: calc(var(--gap) * 2);
}
.filtres .filter-box .filter-base .filter-button-empty > div,
.filtres .filter-box .filter-base .filter-button-use > div,
.filtres .filter-box .filter-base .filter-total-value > div {
  font-weight: 700;
  font-size: 0.5rem;
  text-transform: uppercase;
  display: flex;
  border: 0;
  align-items: center;
  justify-content: center;
  height: var(--button-action);
}
.filtres .filter-box .filter-base .filter-button-use .executar-filtre {
  color: white;
  background: var(--back-corpo);
  aspect-ratio: 1;
  border-radius: calc(var(--button-action) * 0.5);
  opacity: 0;
  text-align: center;
  transition: all 0.6s;
}
.filtres .filter-box .filter-base .filter-button-use .executar-filtre.obert {
  opacity: 1;
}
.filtres .filter-box .filter-base .filter-total-value {
  padding: 0;
  line-height: var(--button-action);
  color: black;
  width: -moz-fit-content;
  width: fit-content;
  background: transparent;
  font-weight: 700;
  font-size: 0.7rem;
  text-transform: uppercase;
}
.filtres .filter-box .filter-base .filter-button-empty > div {
  color: white;
  background: black;
  aspect-ratio: 1;
  border-radius: calc(var(--button-action) * 0.5);
  text-align: center;
  font-size: 0.5rem;
  text-transform: uppercase;
}
.filtres .maxi-filtres-button {
  background-image: url(../img/fs_x_b.svg);
  background-position: center;
  background-size: 40%;
  background-repeat: no-repeat;
  display: block;
  aspect-ratio: 1;
  position: absolute;
  top: 1em;
  right: 3.8em;
  width: 2em;
  border-radius: 50%;
  background-color: black;
  text-indent: -100vw;
  overflow: hidden;
  z-index: 2;
}
.filtres .close-filtres-button {
  position: absolute;
  top: 1em;
  right: 1em;
  width: 2em;
  border-radius: 50%;
  background: black;
  text-indent: -100vw;
  overflow: hidden;
  aspect-ratio: 1;
  background-image: url(../img/close_b.svg);
  background-position: center;
  background-size: 40%;
  background-repeat: no-repeat;
  z-index: 2;
}

.modal-fitxa {
  position: fixed;
  overflow: hidden;
  top: 100vh;
  left: 50%;
  transform: translateX(-50%);
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0vw 0vh 20vw 3vw rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  transition: all 0.6s;
  z-index: 100;
  opacity: 0;
}
@media (min-width: 1024px) {
  .modal-fitxa {
    width: calc(100vw - var(--page-horizontal-margin) * 6);
    height: calc(100vh - (var(--header-top) * 0.25 + 1rem));
  }
}
.modal-fitxa .modal-content {
  overflow: auto;
  height: 100%;
  scroll-behavior: smooth;
  position: absolute;
  top: 0;
  left: 0;
  background: white;
  padding-left: 0;
  width: 100%;
  transition: all 0.6s;
}
.modal-fitxa .modal-content a {
  text-decoration: none;
  color: black;
  transition: all 0.6s;
}
.modal-fitxa .modal-content header {
  position: sticky;
  top: 1.7rem;
  margin-top: 25vh;
  z-index: 1;
  transition: all 0.6s;
  min-height: calc(var(--font-l) * 1.5 * 1.1 + var(--font-l));
  display: flex;
  text-transform: none;
  flex-direction: column;
  justify-content: flex-end;
  padding-left: 2rem;
  height: auto;
  width: 30em;
  padding-bottom: 2rem;
  color: white;
  padding-right: 0;
}
.modal-fitxa .modal-content header .header-content {
  min-height: calc(var(--font-m-et-plus) * 6.6);
  display: flex;
  flex-direction: column;
}
.modal-fitxa .modal-content header .fitxa-titol {
  font-size: var(--font-m-et-plus);
  font-weight: 600;
  margin: 0;
  line-height: 1.1;
  transition: all 0.6s;
  text-transform: none;
  color: inherit;
}
.modal-fitxa .modal-content header .codi {
  font-size: var(--font-m-et-plus);
  transition: all 0.6s;
  margin: 0;
  line-height: 1.1;
  color: inherit;
}
.modal-fitxa .modal-content header.sticky {
  color: var(--back-corpo);
  width: 19em;
  pointer-events: none;
}
.modal-fitxa .modal-content header.sticky .fitxa-titol, .modal-fitxa .modal-content header.sticky .codi {
  font-size: var(--font-m);
}
.modal-fitxa .modal-content article {
  background: white;
  position: relative;
  z-index: 0;
  padding-left: 2rem;
  display: flex;
  overflow: visible;
  align-items: flex-start;
  gap: 2rem;
  padding-right: 0;
}
.modal-fitxa .modal-content aside {
  position: sticky;
  flex-basis: 30%;
  top: calc(3.5vh + var(--font-l) * 0.5 + 4rem);
  margin-top: 15vh;
  transition: all 0.6s;
  font-size: var(--font-s);
  line-height: 1.3;
  text-transform: uppercase;
  padding-right: var(--gap);
}
.modal-fitxa .modal-content .contingut_modal {
  flex-basis: 70%;
}
.modal-fitxa .modal-content .bloc {
  padding-top: calc(3.5vh + var(--font-l) * 0.5 + 4rem);
  padding-right: 20%;
  font-size: var(--font-s);
  line-height: 1.4;
}
.modal-fitxa .modal-content .bloc:first-child {
  padding-top: 15vh;
}
.modal-fitxa .modal-content .bloc#documentacio {
  padding-bottom: 50vh;
}
.modal-fitxa .modal-content .bloc#documentacio ul li {
  color: transparent;
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
}
.modal-fitxa .modal-content .bloc#documentacio ul li a {
  width: calc(var(--button-action) * 2);
  aspect-ratio: 0.77;
  position: relative;
  height: auto;
  display: inline-block;
  border: 1px solid black;
  font-size: 1.5em;
  padding: 0.5em;
  margin-bottom: var(--gap);
  display: inline-flex;
  justify-content: center;
  font-weight: 600;
  align-items: center;
  line-height: 1;
  color: grey;
  overflow: hidden;
}
.modal-fitxa .modal-content .bloc#documentacio ul li a:after {
  content: "PDF";
  display: flex;
  align-items: center;
  font-size: 0.5em;
  padding: 1em;
  font-weight: 700;
  aspect-ratio: 1;
  background: red;
  color: white;
  transform: rotate(45 deg);
  position: absolute;
  top: 0;
  left: 0;
}
.modal-fitxa .modal-content .bloc#documentacio ul li a:hover {
  color: white;
  background: var(--deep-grey);
}
.modal-fitxa .modal-content .bloc#documentacio .alerta-cc {
  max-width: 50%;
}
.modal-fitxa .modal-content .bloc#documentacio .alerta-cc img {
  margin-right: var(--gap);
}
.modal-fitxa .modal-content .bloc#documentacio dl {
  margin-bottom: 4rem;
}
.modal-fitxa .modal-content .bloc h3 {
  font-size: calc(var(--font-s) * 0.8);
  font-weight: 600;
  text-transform: uppercase;
  color: var(--back-corpo);
  margin-bottom: 0em;
  margin-top: 2em;
}
.modal-fitxa .modal-content .bloc h3:first-child {
  margin-top: 0;
}
.modal-fitxa .modal-content .menu_modal {
  margin: 0;
  padding: 0;
  list-style: none;
}
.modal-fitxa .modal-content .menu_modal li {
  transition: all 0.3s;
}
.modal-fitxa .modal-content .menu_modal li:hover {
  font-weight: 700;
}
.modal-fitxa .modal-content .menu_modal li.active {
  font-weight: 700;
}
.modal-fitxa .modal-content .menu_modal li.active a {
  color: var(--back-corpo);
}
.modal-fitxa .modal-content dl {
  margin: 0;
  padding-top: 1rem;
  margin-bottom: 2rem;
  display: grid;
  grid-template-columns: 1fr 3fr;
  transition: all 0.6s;
  border-left: 1px solid var(--back-corpo);
  padding-left: calc(var(--gap) * 0.66);
  font-size: calc(var(--font-s) * 0.8);
}
.modal-fitxa .modal-content dl dt {
  grid-column: 1;
  margin-bottom: 0.5em;
}
.modal-fitxa .modal-content dl dd {
  grid-column: 2;
  font-weight: 600;
}
.modal-fitxa .modal-content dl dd.bloc-any {
  border-top: 1px solid #ddd;
  padding-top: 1em;
  margin-top: 1em;
}
.modal-fitxa .modal-content dl dd.bloc-any:first-of-type {
  border-top: 0;
  padding-top: 0;
  margin-top: 0;
}
.modal-fitxa .modal-content dl.full dd, .modal-fitxa .modal-content dl.resultats dd {
  grid-column: 1/span 2;
  margin-left: 0;
  margin-bottom: 1em;
  font-weight: inherit;
}
.modal-fitxa .modal-content dl.full dd br,
.modal-fitxa .modal-content dl.full dd .line-break, .modal-fitxa .modal-content dl.resultats dd br,
.modal-fitxa .modal-content dl.resultats dd .line-break {
  display: block;
  margin-top: 1em;
  margin-bottom: 1em;
}
.modal-fitxa .modal-content .content-map #map_fitxa {
  height: calc(25vh + var(--font-m-et-plus) * 6.6 + 2rem - var(--gap) * 0.5);
}
.modal-fitxa .modal-content #informacio_tecnica_i_legal dd:has(> *:contains("Any:")),
.modal-fitxa .modal-content #informacio_tecnica_i_legal dd:has(> :contains("Any:")),
.modal-fitxa .modal-content #informacio_tecnica_i_legal dd:has(> span:contains("Any:")) {
  border-top: 1px solid;
  padding-top: var(--gap);
}
.modal-fitxa .maxi-modal-button {
  background-image: url(../img/fs_x_b.svg);
  background-position: center;
  background-size: 24%;
  border-radius: 50%;
  background-repeat: no-repeat;
  display: none;
  aspect-ratio: 1;
  position: absolute;
  top: calc(var(--gap) + var(--font-m) * 2 * 0.95 - (var(--header-top) * 0.5 + 2rem) * 0.5);
  width: calc((var(--button-action) - 2px) * 0.7);
  right: calc(var(--button-action) - 2px + 2em);
  background-color: var(--back-corpo);
  text-indent: -100vw;
  overflow: hidden;
  z-index: 2;
  transition: all 0.6s;
}
@media (min-width: 1024px) {
  .modal-fitxa .maxi-modal-button {
    display: block;
  }
}
.modal-fitxa .maxi-modal-button:hover {
  background-color: var(--back-corpo);
}
.modal-fitxa.oberta {
  top: 0;
  opacity: 1;
}
@media (min-width: 1024px) {
  .modal-fitxa.oberta {
    top: calc((var(--header-top) * 0.25 + 1rem) * 0.5);
  }
}
.modal-fitxa.maxi {
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  transform: translateX(0);
  border-radius: 0;
  background: rgb(255, 255, 255);
}
.modal-fitxa.maxi header {
  width: 30%;
  top: 12em;
  width: 30%;
}
.modal-fitxa.maxi header .fitxa-titol,
.modal-fitxa.maxi header .codi {
  font-size: calc(var(--font-l) * 1.2);
}
.modal-fitxa.maxi .modal-content {
  padding-top: 1vh;
}
.modal-fitxa.maxi .modal-content aside {
  top: calc(var(--font-m-et-plus) * 0.5 + 16em);
}
.modal-fitxa.maxi .modal-content dl {
  font-size: calc(var(--font-s) * 1.1);
}
.modal-fitxa.maxi .modal-content .bloc {
  padding-top: calc(var(--font-m-et-plus) * 0.5 + 16em);
}
.modal-fitxa.maxi .modal-content .content-map #map_fitxa {
  height: calc(25vh + var(--font-m-et-plus) * 6.6 + 3rem - var(--gap) * 0.5);
}
.modal-fitxa.maxi .modal-content header.sticky {
  top: 1.7rem;
}
.modal-fitxa.maxi .modal-content header.sticky .fitxa-titol, .modal-fitxa.maxi .modal-content header.sticky .codi {
  font-size: calc(var(--font-l) * 1.2);
}
.modal-fitxa.maxi .maxi-modal-button {
  background-image: url(../img/fs_m_b.svg);
}
.modal-fitxa .close-modal-button {
  position: absolute;
  top: calc(var(--gap) + var(--font-m) * 2 * 0.95 - (var(--header-top) * 0.5 + 2rem) * 0.5);
  right: 1em;
  width: calc((var(--button-action) - 2px) * 0.7);
  border-radius: 50%;
  background-color: var(--back-corpo);
  text-indent: -100vw;
  overflow: hidden;
  aspect-ratio: 1;
  background-image: url(../img/close_b.svg);
  background-position: center;
  background-size: 23%;
  background-repeat: no-repeat;
  z-index: 2;
  transition: all 0.6s;
}
.modal-fitxa .close-modal-button:hover {
  background-color: var(--back-corpo);
}

.content.paraules {
  padding-top: calc(4vh + 12rem);
  padding-left: calc(12rem + 4vw);
  padding-right: 10vw;
}

/** afegit dani **/
/** paginacio llista */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.pagination button {
  margin: 0 10px;
  padding: 5px 10px;
}

.path-mapa .coeli-search-filters {
  display: block;
  opacity: 1;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box {
  position: fixed;
  top: 0;
  right: 0;
  left: auto;
  padding: 0;
  transform: translateY(-100%);
  width: 100%;
  height: var(--button-action);
  border-radius: 0px;
  opacity: 1;
  z-index: 10;
  background-color: rgba(34, 34, 34, 0);
  -webkit-backdrop-filter: blur(0px);
          backdrop-filter: blur(0px);
  transition: all 1.2s 0.6s;
  box-shadow: 0vh 0 0vh rgba(0, 0, 0, 0.5);
}
.path-mapa .coeli-search-filters .coeli-result-filter-box:before {
  display: none;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box > * {
  opacity: 0;
  transition: opacity 0.6s 0s;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  height: 100%;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group {
  position: relative;
  width: calc((100vw - var(--page-margin) * 2 - var(--gap) * 1 * 7) / 8);
  max-height: calc(100vh - (6rem + var(--button-action) + 2rem - 2px + var(--button-action) * 1.5));
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group:before {
  display: block;
  content: "";
  background-color: #555;
  width: 1px;
  height: 100%;
  position: absolute;
  left: calc(var(--gap) * -0.5);
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group:first-child:before {
  display: none;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-filter-type-title {
  padding: 0;
  margin: 0;
  margin-bottom: 1rem;
  height: 2rem;
  line-height: 2rem;
  border-bottom: 1px solid #555;
  color: white;
  font-size: 0.9em;
  text-transform: uppercase;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list {
  height: auto;
  overflow: visible;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul {
  position: absolute;
  top: 0;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  max-height: calc(98vh - (9rem + var(--filter-base) + var(--button-action) * 1.5));
  overflow: auto;
  width: 100%;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li {
  display: block;
  padding: 0;
  margin: 0;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li:first-child .term-label {
  border: 0;
  margin-top: 0;
  padding-top: 0;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li:has(input[type=checkbox]:checked) {
  margin-bottom: 1px;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li:has(input[type=checkbox]:checked) + label:after {
  display: none;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li .term-label {
  color: white;
  border-top: 1px dotted #555;
  display: block;
  padding-top: 1em;
  width: calc(100% - 0.3em);
  margin-top: 1em;
  padding-bottom: 0.5em;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li label {
  margin: 0;
  padding: 0.2em;
  border-radius: 0;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li label:hover {
  background: var(--back-corpo);
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li [type=checkbox]:checked + label {
  background: var(--back-corpo);
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li [type=checkbox]:checked + label:after {
  display: none;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Area-funeraria],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Area-productiva_Agricola],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Oci-i-Espectacles],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Sistema-hidraulic],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Obra-publica-i-civil],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Material-descontextualitzat_],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Higiene-i-Salut],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Habitat],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Estructures-defensives-i-militars],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Estratigrafia],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Edifici-de-culte-o-religios],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Edifici-administratiu],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Bens-Immobles-aillats],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Material-descontextualitzat],
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Area-productiva] {
  order: 1;
  padding-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Area-funeraria].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Area-productiva_Agricola].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Oci-i-Espectacles].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Sistema-hidraulic].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Obra-publica-i-civil].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Material-descontextualitzat_].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Higiene-i-Salut].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Habitat].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Estructures-defensives-i-militars].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Estratigrafia].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Edifici-de-culte-o-religios].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Edifici-administratiu].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Bens-Immobles-aillats].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Material-descontextualitzat].Patrimoni-Immoble,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Area-productiva].Patrimoni-Immoble {
  padding-left: 0px !important;
  border-bottom: 1px dotted white;
  display: block !important;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Patrimoni-Immoble {
  display: none !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Patrimoni-Moble-Aillat_] {
  padding-left: 10px !important;
  border-bottom: 0px solid white;
  padding-top: 0em !important;
  margin-bottom: 0 !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Patrimoni-Moble-Aillat {
  order: 2;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul li[class*=Patrimoni-Natural_] {
  border-bottom: 0px solid white;
  padding: 0em !important;
  margin-bottom: 0 !important;
  padding-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Patrimoni-Natural {
  order: 2;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Preurba_Prehistoria {
  order: 1;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Preurba_Protohistoria {
  order: 3;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Prehistoria,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Neolitic,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Bronze,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Mig\.-4000---3300,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Calcolitic\.-2900 {
  order: 2;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Protohistoria {
  order: 4;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Preurba_218---10-a\.C\. {
  order: 5;
  margin-left: 10px !important;
  border: none;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Urba_Contemporani-\(Metropolita\) {
  order: 10;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Urba_Roma-imperi {
  order: 9;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Urba_Antiguitat-tardana {
  order: 11;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Urba_Medieval, .path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Urba_Predomini-del-Consell-de-Cent {
  order: 13;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Urba_Modern {
  order: 15;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Urba_Contemporani {
  order: 19;
  border-bottom: 1px dotted white;
  padding-top: 1em !important;
  margin-bottom: 0.5em !important;
  padding-bottom: 0.2em !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Roma-imperi {
  order: 10;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Antiguitat-tardana {
  order: 12;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Medieval,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Predomini-del-Consell-de-Cent {
  order: 14;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Modern {
  order: 16;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul ._Contemporani-\(Metropolita\) {
  order: 17;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Contemporani {
  order: 18;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul .Contemporani-\(Metropolita\) {
  order: 20;
  margin-left: 10px !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul ._Preurba,
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-list .coeli-filter-list-group .coeli-sub-list ul ._Urba {
  display: none !important;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base {
  background: transparent !important;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  grid-template-columns: auto 10vw auto;
  filter: none;
  gap: var(--gap);
  height: calc(var(--button-action) + 2rem - 2px);
  padding: 1rem;
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base .coeli-filter-total-value {
  color: white;
  text-transform: uppercase;
  font-size: 0.9rem;
  font-weight: 700;
  flex-direction: row;
  gap: calc(var(--gap) * 0.5);
}
@media (min-width: 1024px) {
  .path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base .coeli-filter-total-value {
    grid-column: 1;
    justify-self: flex-end;
  }
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base .coeli-filter-total-value.coeli-loading-filtres i {
  left: 0;
  transform: translate(-160%, -50%);
}
@media (min-width: 1024px) {
  .path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base .coeli-filter-button-empty {
    grid-column: 3;
    justify-self: inherit;
  }
}
.path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base .coeli-filter-button-empty button {
  color: white;
  text-transform: uppercase;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  height: calc(var(--button-action) - 2px);
  border-radius: 0;
}
@media (min-width: 1024px) {
  .path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base .coeli-filter-button-use {
    grid-column: 2;
    justify-self: inherit;
  }
  .path-mapa .coeli-search-filters .coeli-result-filter-box .coeli-filter-base .coeli-filter-button-use button {
    background: var(--back-corpo);
    color: white;
    height: calc(var(--button-action) - 2px);
    border-radius: calc((var(--button-action) - 2px) * 0.5);
    font-size: 0.9rem;
    margin: 0;
    text-transform: uppercase;
    font-weight: 700;
    cursor: pointer;
  }
}
.path-mapa .coeli-search-filters.active-filters {
  z-index: -1;
}
.path-mapa .coeli-search-filters.active-filters .coeli-result-filter-box {
  position: fixed;
  color: #000;
  width: 100vw;
  transform: translateY(0);
  box-shadow: 5vh 0 20vh rgba(0, 0, 0, 0.5);
  top: 0;
  right: 0;
  font-weight: 600;
  height: 100vh;
  background: rgba(34, 34, 34, 0.8666666667);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  overflow: auto;
  padding: var(--page-margin);
  padding-top: calc((4vh + 12rem) * 0.5 + var(--button-action));
  padding-bottom: calc(var(--button-action) + 2rem - 2px);
  opacity: 1;
  z-index: 11;
  border-radius: 0;
}
.path-mapa .coeli-search-filters.active-filters .coeli-result-filter-box > * {
  opacity: 1;
  transition: opacity 0.6s 1.2s;
}
.path-mapa .coeli-list-results {
  background: none;
}
.path-mapa .coeli-list-results .coeli-selected-facets {
  display: flex;
  width: 26vw;
  align-items: flex-end;
  align-content: flex-end;
  flex-wrap: wrap;
  gap: 0.2em;
  min-height: 100%;
}
.path-mapa .coeli-list-results .coeli-selected-facets a {
  background: var(--back-corpo);
  border-radius: 2px;
  margin-bottom: 0;
  margin-right: 0;
  text-decoration: none;
  font-size: 0.9em;
  line-height: 0.85;
}
.path-mapa .coeli-result-filter-box-button {
  height: 100%;
}
.path-mapa .open-close-button {
  height: 100%;
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
  cursor: pointer;
  color: white;
  font-size: 0.8em;
  transition: all 0.6s;
}
.path-mapa .open-close-button:after, .path-mapa .open-close-button:before {
  content: "";
  display: block;
  width: 0%;
  height: 0px;
  background: transparent;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(0deg);
  transition: all 0.6s;
}
.path-mapa .open-close-button:after {
  transform: translate(-50%, -50%) rotate(0deg);
}
.path-mapa.active-filters .open-close-button {
  height: 100%;
  overflow: hidden;
  color: transparent;
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}
.path-mapa.active-filters .open-close-button:after, .path-mapa.active-filters .open-close-button:before {
  width: 40%;
  height: 2px;
  background: white;
  transform: translate(-50%, -50%) rotate(45deg);
}
.path-mapa.active-filters .open-close-button:after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.filtrar-button {
  position: fixed;
  border-radius: 0px;
  transform: translateY(-50%);
  border: 0;
  background-color: var(--back-corpo);
  height: calc(var(--button-action) - 2px);
  width: calc(var(--button-action) - 2px);
  overflow: hidden;
  color: white;
  display: flex;
  z-index: 11;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.9em;
  font-weight: 700;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.6s;
  border-radius: 50%;
  left: 4vw;
  top: 26vh;
}
@media (min-width: 1024px) {
  .filtrar-button {
    top: calc(var(--header-top) * 0.25 + 1.2rem + 1px);
    left: calc(100% + var(--gap) * 3);
  }
}
.filtrar-button button {
  background: none;
  border: 0;
  text-transform: uppercase;
  margin: 0;
  font-weight: 400;
  padding: 0;
}
.filtrar-button:hover {
  background-color: var(--back-corpo);
}

.coeli-close-filters {
  display: none;
}

#loading {
  display: none !important;
}

.corpo {
  background-color: rgba(255, 255, 255, 0.8);
  position: fixed;
  border-radius: 0px;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0.7em;
  font-size: calc(var(--font-s) * 0.9);
  z-index: 10;
  font-weight: 300;
}
@media (min-width: 1024px) {
  .corpo {
    width: auto;
    bottom: var(--gap);
    left: calc(var(--header-top) * 0.25 + 1rem);
  }
}
.corpo a {
  color: var(--back-corpo);
  text-decoration: none;
}
.corpo a:hover {
  text-decoration: underline;
  color: var(--back-corpo);
}

.path-stories .titol-pagina a {
  text-decoration: none !important;
  color: black !important;
  pointer-events: none;
}

.path-stories,
.page-node-type-relat {
  background: #222;
}
.path-stories #vue > header .resultat-cerca,
.path-stories #vue > header .llista-button,
.path-stories #no-vue > header .resultat-cerca,
.path-stories #no-vue > header .llista-button,
.page-node-type-relat #vue > header .resultat-cerca,
.page-node-type-relat #vue > header .llista-button,
.page-node-type-relat #no-vue > header .resultat-cerca,
.page-node-type-relat #no-vue > header .llista-button {
  display: none;
}
.path-stories #vue > header .titol-pagina,
.path-stories #no-vue > header .titol-pagina,
.page-node-type-relat #vue > header .titol-pagina,
.page-node-type-relat #no-vue > header .titol-pagina {
  width: -moz-max-content;
  width: max-content;
  padding-right: calc(var(--gap) * 0.5);
}
@media (min-width: 1024px) {
  .path-stories #vue > header .titol-pagina,
  .path-stories #no-vue > header .titol-pagina,
  .page-node-type-relat #vue > header .titol-pagina,
  .page-node-type-relat #no-vue > header .titol-pagina {
    padding-right: var(--gap);
  }
}
.path-stories #vue > header .titol-pagina a,
.path-stories #no-vue > header .titol-pagina a,
.page-node-type-relat #vue > header .titol-pagina a,
.page-node-type-relat #no-vue > header .titol-pagina a {
  color: var(--back-corpo);
}
.path-stories main,
.page-node-type-relat main {
  background: white;
}
.path-stories .filtrar-button,
.path-stories .cercador,
.path-stories #block-coeli-map-theme-content .coeli-list-results,
.page-node-type-relat .filtrar-button,
.page-node-type-relat .cercador,
.page-node-type-relat #block-coeli-map-theme-content .coeli-list-results {
  display: none;
}
.path-stories .relats-list ul,
.page-node-type-relat .relats-list ul {
  margin: 0;
  padding: 0;
  padding-top: 10vh;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--gap);
}
.path-stories .relats-list li,
.page-node-type-relat .relats-list li {
  background: linear-gradient(120deg, #d96c00, #ae2f00);
  margin: 0;
  padding: 0;
  filter: drop-shadow(0 0rem 0rem rgba(0, 0, 0, 0));
  transition: all 0.6s;
}
.path-stories .relats-list li:hover,
.page-node-type-relat .relats-list li:hover {
  transform: translateY(-1rem);
  filter: drop-shadow(0 1.5rem 2rem #000);
}
.path-stories .relats-list li:hover a.veure-fitxa,
.page-node-type-relat .relats-list li:hover a.veure-fitxa {
  transform: translateY(-0.3rem);
  background: white;
  color: var(--back-corpo);
  filter: drop-shadow(0 0.9rem 0.9rem rgba(0, 0, 0, 0.4));
}
.path-stories .relats-list li h2,
.page-node-type-relat .relats-list li h2 {
  font-size: calc(var(--font-l) * 0.4);
  font-weight: 700;
  margin: 0;
  line-height: 1.1;
  color: var(--back-modal);
  text-transform: none;
  padding-left: 1rem;
  padding-top: 1rem;
  padding-right: 1rem;
}
@media (min-width: 1024px) {
  .path-stories .relats-list li h2,
  .page-node-type-relat .relats-list li h2 {
    font-size: calc(var(--font-l) * 0.7);
    padding-left: 2rem;
    padding-top: 2rem;
    padding-right: 2rem;
  }
}
.path-stories .relats-list li p,
.page-node-type-relat .relats-list li p {
  font-size: calc(var(--font-m) * 0.9);
  font-weight: 300;
  color: var(--back-modal);
  padding-left: 1rem;
  padding-top: 1rem;
  padding-right: 1rem;
}
@media (min-width: 1024px) {
  .path-stories .relats-list li p,
  .page-node-type-relat .relats-list li p {
    padding-left: 2rem;
    padding-top: 2rem;
    padding-right: 2rem;
  }
}
.path-stories .relats-list li p:last-child,
.page-node-type-relat .relats-list li p:last-child {
  padding-bottom: 1rem;
}
@media (min-width: 1024px) {
  .path-stories .relats-list li p:last-child,
  .page-node-type-relat .relats-list li p:last-child {
    padding-bottom: 2rem;
  }
}
.path-stories .relats-list li a,
.page-node-type-relat .relats-list li a {
  color: white;
  text-decoration: none;
}
.path-stories .relats-list li a.veure-fitxa,
.page-node-type-relat .relats-list li a.veure-fitxa {
  color: white;
  text-decoration: none;
  border: 1px solid;
  padding-left: 2em;
  padding-right: 2em;
  line-height: 2em;
  display: inline-block;
  border-radius: 0.2em;
  font-weight: 600;
  transition: all 0.6s 0.3s;
}
.path-stories .relats-list li .imatge,
.page-node-type-relat .relats-list li .imatge {
  background: #c2c2c2;
  width: 100%;
}
.path-stories .relats-list li .imatge img,
.page-node-type-relat .relats-list li .imatge img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  display: block;
}
.path-stories .relats-list li.col-1 .imatge,
.page-node-type-relat .relats-list li.col-1 .imatge {
  width: 100%;
  aspect-ratio: 1.6;
}
.path-stories .relats-list li.col-2,
.page-node-type-relat .relats-list li.col-2 {
  grid-column: span 2;
  display: grid;
  max-width: 100%;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr auto auto;
  gap: var(--gap);
  row-gap: 0;
}
.path-stories .relats-list li.col-2 .imatge,
.page-node-type-relat .relats-list li.col-2 .imatge {
  grid-column: span 1;
  grid-row: span 3;
  aspect-ratio: 1;
}
.path-stories .relats-list li.col-2 h2,
.page-node-type-relat .relats-list li.col-2 h2 {
  grid-column: 2;
  grid-row: 1;
}
.path-stories .relats-list li.col-2 P,
.page-node-type-relat .relats-list li.col-2 P {
  grid-column: 2;
}
.path-stories .relats-list li.col-2 P:last-child,
.page-node-type-relat .relats-list li.col-2 P:last-child {
  grid-column: 2;
}
.path-stories .relats-list li.col-2.full-img .imatge,
.page-node-type-relat .relats-list li.col-2.full-img .imatge {
  grid-column: span 2;
  grid-row: 1;
  aspect-ratio: 2;
  grid-row: 1;
}
.path-stories .relats-list li.col-2.full-img h2,
.page-node-type-relat .relats-list li.col-2.full-img h2 {
  grid-column: span 2;
  grid-row: 2;
}
.path-stories .relats-list li.col-2.full-img p,
.page-node-type-relat .relats-list li.col-2.full-img p {
  grid-column: span 2;
  grid-row: 3;
}
.path-stories .relats-list li.col-2.full-img p:last-child,
.page-node-type-relat .relats-list li.col-2.full-img p:last-child {
  grid-column: span 2;
  grid-row: 3;
}
.path-stories main,
.page-node-type-relat main {
  margin-bottom: 10vh;
}

nav.pager {
  width: 100%;
}
nav.pager .pager__items {
  margin-top: 4rem;
  border-top: 2px solid white;
  padding-top: 1rem;
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
}
nav.pager .pager__items a {
  color: white;
  font-size: var(--font-m);
}
nav.pager .pager__items .pager__item--previous {
  position: absolute;
  left: 0;
}
nav.pager .pager__items .pager__item--next {
  position: absolute;
  right: 0;
}
nav.pager .pager__items .pager__item--first, nav.pager .pager__items .pager__item--last {
  display: none;
}

.path-stories main {
  background: #222;
}

.page-node-type-relat .content-map-relat {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: -1;
}
.page-node-type-relat .content-map-relat .mapboxgl-map {
  width: 100%;
  height: 100%;
}
.page-node-type-relat .content-map-relat .mapboxgl-map .mapboxgl-canvas-container {
  width: 100%;
  height: 100%;
}
.page-node-type-relat main {
  margin-bottom: 80vh;
  padding-left: 0;
  padding-right: 0;
}
.page-node-type-relat main header {
  padding-left: calc(var(--page-horizontal-margin) * 0.5);
  padding-right: calc(var(--page-horizontal-margin) * 0.5);
  width: 100vw;
  height: 100vh;
  position: relative;
  z-index: 1;
}
@media (min-width: 1024px) {
  .page-node-type-relat main header {
    padding-left: calc(var(--page-horizontal-margin) * 3);
    padding-right: calc(var(--page-horizontal-margin) * 3);
  }
}
.page-node-type-relat main header h1 {
  margin: 0;
  font-size: calc(var(--font-l) * 0.75);
  color: white;
  line-height: 1.2;
  background: linear-gradient(120deg, #d96c00, #ae2f00);
  padding: 2rem;
  position: absolute;
  bottom: 5rem;
  left: calc(var(--page-horizontal-margin) * 0.5);
  right: calc(var(--page-horizontal-margin) * 0.5);
}
@media (min-width: 1024px) {
  .page-node-type-relat main header h1 {
    left: calc(var(--page-horizontal-margin) * 3);
    right: calc(var(--page-horizontal-margin) * 3);
    font-size: calc(var(--font-l) * 1.5);
    bottom: 5rem;
  }
}
.page-node-type-relat main header figure {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #222;
  margin: 0;
  z-index: -1;
}
.page-node-type-relat main header figure img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.page-node-type-relat main article,
.page-node-type-relat main .body-content {
  padding-top: 6rem;
  background: white;
  position: relative;
  padding: 6rem;
  font-size: calc(var(--font-m) * 1.2);
  padding-left: calc(var(--page-horizontal-margin) * 0.5);
  padding-right: calc(var(--page-horizontal-margin) * 0.5);
}
@media (min-width: 1024px) {
  .page-node-type-relat main article,
  .page-node-type-relat main .body-content {
    padding: 6rem;
    padding-left: calc(var(--page-horizontal-margin) * 3);
    padding-right: calc(var(--page-horizontal-margin) * 3);
  }
}
.page-node-type-relat main article .paragraph,
.page-node-type-relat main .body-content .paragraph {
  background: transparent !important;
  font-size: calc(var(--font-m));
  padding: 0;
}
.page-node-type-relat main article .paragraph .paragraph-title h2,
.page-node-type-relat main .body-content .paragraph .paragraph-title h2 {
  font-size: var(--font-m);
  text-transform: none;
  font-weight: 700;
  margin-bottom: 1rem;
  margin-top: 4rem;
  text-align: left;
}
.page-node-type-relat main article .paragraph .pager-loadmore,
.page-node-type-relat main .body-content .paragraph .pager-loadmore {
  display: none !important;
}
.page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria,
.page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria .info,
.page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria .info {
  display: none !important;
}
.page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria .media-list,
.page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria .media-list {
  width: 100% !important;
  height: auto !important;
  aspect-ratio: 1;
}
@media (min-width: 1024px) {
  .page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria .media-list,
  .page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria .media-list {
    aspect-ratio: 16/9;
  }
}
.page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria .media-list .media-video, .page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria .media-list .media-img,
.page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria .media-list .media-video,
.page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria .media-list .media-img {
  width: 100%;
  height: 100%;
  opacity: 1;
  pointer-events: none;
  aspect-ratio: 1;
}
@media (min-width: 1024px) {
  .page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria .media-list .media-video, .page-node-type-relat main article .paragraph.paragraph--type--paragraph-galeria .media-list .media-img,
  .page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria .media-list .media-video,
  .page-node-type-relat main .body-content .paragraph.paragraph--type--paragraph-galeria .media-list .media-img {
    aspect-ratio: 16/9;
  }
}
.page-node-type-relat main article p,
.page-node-type-relat main .body-content p {
  line-height: 1.6;
  margin-top: 2rem;
}
.page-node-type-relat main article figure,
.page-node-type-relat main .body-content figure {
  margin: 0;
  padding: 0;
  margin-bottom: 4rem;
}
.page-node-type-relat main article figure img,
.page-node-type-relat main .body-content figure img {
  width: 100%;
  height: auto;
  max-height: 30vh;
}
.page-node-type-relat main article figure figcaption,
.page-node-type-relat main .body-content figure figcaption {
  margin-top: 1rem;
  font-size: 0.8em;
}
.page-node-type-relat main article iframe,
.page-node-type-relat main .body-content iframe {
  width: 100% !important;
  height: auto !important;
  aspect-ratio: 1.77;
  overflow: hidden;
}

.path-frontpage {
  background: var(--back-modal);
}
.path-frontpage .paragraph--type--paragraph-intro .field-paragraph-intro-text a {
  display: block;
  aspect-ratio: 1;
  display: flex;
  border-radius: 50%;
  width: 6rem;
  color: white;
  background-color: var(--back-corpo);
  align-items: center;
  justify-content: center;
  text-decoration: none;
  text-transform: uppercase;
}
.path-frontpage .paragraph--type--paragraph-intro .field-paragraph-intro-text a span {
  font-weight: 700 !important;
}
.path-frontpage .paragraph--type--paragraph-intro .field-paragraph-intro-text a:hover {
  color: white;
}

.path-node.page-node-type-pagina-basica #vue > header {
  /*    .llista-button, .titol-pagina {
          cursor: pointer;
          background: var(--back-corpo);
          color: transparent;
          height: calc(var(--button-action) - 2px);
          top: calc(var(--header-top) * 0.25 + 1rem);
          width: calc(var(--button-action) - 2px);
          border-radius: 0px;
          padding: 0.5em;
          grid-column: 3;
          right: 10vw;
          display: flex;
          z-index: 9;
          align-items: center;
          justify-content: center;
          text-align: center;
          line-height: 1.1;
          font-size: 0.8em;
          font-weight: 400;
          text-transform: uppercase;
          transition: all 0.6s;
          border-radius: 50%;

          a {
              color:white;
          }
      }
          */
}
.path-node.page-node-type-pagina-basica #vue > header .titol-pagina {
  grid-column: 2;
}
.path-node.page-node-type-pagina-basica .paragraph {
  opacity: 1;
  transform: translateY(0);
}
.path-node.page-node-type-pagina-basica p {
  font-size: calc(var(--font-m));
  line-height: 1.6;
}
.path-node.page-node-type-pagina-basica .paragraph--type--paragraph-destacat .contingut-destacat > .row > .informacio {
  padding-left: 0;
}
.path-node.page-node-type-pagina-basica .paragraph--type--paragraph-destacat .contingut-destacat > .row > .informacio h2 {
  margin-top: 4.1rem;
  font-weight: 700;
  text-transform: none;
  color: var(--back-corpo);
}
.path-node.page-node-type-pagina-basica .paragraph {
  padding-top: 0 !important;
  background: transparent;
  font-size: calc(var(--font-m));
}

.path-frontpage #page-aglaia-wrapper {
  display: block;
  grid-template-columns: repeat(14, 1fr);
  gap: var(--gap);
  padding-left: calc(var(--page-horizontal-margin) * 0.5);
  padding-right: calc(var(--page-horizontal-margin) * 0.5);
}
@media (min-width: 1024px) {
  .path-frontpage #page-aglaia-wrapper {
    display: grid;
    padding-left: 0;
    padding-right: 0;
  }
}
.path-frontpage #vue {
  grid-column: 2/span 5;
  background: transparent;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0;
  padding-top: 6rem;
  padding-bottom: 2em;
}
@media (min-width: 1024px) {
  .path-frontpage #vue {
    padding: 10rem 0 8rem;
  }
}
.path-frontpage #vue > header {
  position: static;
  background: transparent;
  padding: 0;
}
@media (min-width: 1024px) {
  .path-frontpage #vue > header {
    padding: 0.7rem;
    padding-right: 1em;
  }
}
.path-frontpage #vue > header #main-title {
  border: 0;
  font-size: calc(var(--font-l) * 1);
}
@media (min-width: 1024px) {
  .path-frontpage #vue > header #main-title {
    font-size: calc(var(--font-xl) * 1);
  }
}
.path-frontpage #vue > header #main-title span {
  display: block;
  letter-spacing: -0.06em;
  font-size: calc(var(--font-m-et-plus) * 1);
}
@media (min-width: 1024px) {
  .path-frontpage #vue > header #main-title span {
    font-size: calc(var(--font-l) * 1);
  }
}
.path-frontpage #vue > header .titol-pagina,
.path-frontpage #vue > header .llista-button {
  display: none;
}
.path-frontpage .highlighted {
  display: none;
}
.path-frontpage main {
  grid-column: 7/span 6;
  padding: 0;
  background: transparent;
  grid-row: 1;
}
.path-frontpage main .field-paragraph-intro-text {
  text-align: left;
}
.path-frontpage .paragraph {
  opacity: 1;
}
.path-frontpage .paragraph--type--paragraph-intro {
  padding: 0;
}
@media (min-width: 1024px) {
  .path-frontpage .paragraph--type--paragraph-intro {
    padding: 10rem 0 8rem;
  }
}
.path-frontpage .paragraph--type--paragraph-intro p {
  margin: 0;
}
@media (min-width: 1024px) {
  .path-frontpage .paragraph--type--paragraph-intro p {
    margin-top: 1em;
    margin-bottom: 2.5rem;
  }
}
.path-frontpage .paragraph--type--paragraph-intro .field-paragraph-intro-titol {
  text-align: left;
  margin-bottom: 1em;
  margin-top: 0;
  padding: 0;
  font-size: calc(var(--font-m) * 1.2);
  font-weight: 700;
}
@media (min-width: 1024px) {
  .path-frontpage .paragraph--type--paragraph-intro .field-paragraph-intro-titol {
    margin-bottom: 0;
  }
}
.path-frontpage .paragraph--type--paragraph-intro .field-paragraph-intro-text p {
  font-size: calc(var(--font-m));
  line-height: 1.6;
}

body .aglaia-go-top {
  width: var(--button-action);
  height: var(--button-action);
  right: 4vw;
  padding: 0;
}
body .aglaia-go-top:before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  background-image: url(/cartaarqueologica/web/modules/custom/mapa_module/img/arrow-up.svg);
  width: 33%;
  height: 33%;
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
  display: block;
  z-index: 2;
  transition: all 0.6s;
}
body .aglaia-go-top:hover:before {
  transform: translate(-50%, -70%);
}
body .aglaia-go-top .go-top {
  width: 100%;
  height: 100%;
  position: relative;
}
body .aglaia-go-top .go-top .b,
body .aglaia-go-top .go-top .f,
body .aglaia-go-top .go-top .c {
  width: 100%;
  height: 100%;
}
body .aglaia-go-top .go-top .c {
  fill: var(--back-corpo) !important;
  stroke: var(--back-corpo) !important;
}
body .aglaia-go-top .go-top .d {
  display: none;
}
body .aglaia-go-top .sr-only {
  display: none;
}

.timeline {
  position: fixed;
  z-index: 10;
  bottom: 10vh;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 0;
  opacity: 0.6;
  transition: opacity 0.6s;
  display: none;
}
@media (min-width: 1024px) {
  .timeline {
    display: block;
  }
}
.timeline:hover {
  opacity: 1;
}
.timeline #grafo {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  margin: 0;
  height: 0px !important;
}
.timeline #grafo:after {
  width: 100%;
  height: 1px;
  background: var(--back-corpo);
  content: "";
  display: bloc;
  position: absolute;
  top: 50%;
  left: 0;
}
.timeline #grafo li {
  position: relative;
  margin: 0;
  padding: 0;
  pointer-events: none;
}
.timeline #grafo li span {
  opacity: 0.4;
  position: absolute;
  border-radius: 50%;
  transition: background-color 0.6s;
  background: var(--back-corpo);
}

.toolbar-lining,
.toolbar-bar {
  width: 100%;
}

#block-aglaia-subtheme-primary-local-tasks {
  background: white !important;
  position: fixed;
  top: 40px;
  right: 0;
  height: 3rem;
  width: 3rem;
  z-index: 20;
  overflow: hidden;
  padding-top: 3rem;
}
#block-aglaia-subtheme-primary-local-tasks:hover {
  width: 15rem;
  height: 100%;
}

.toolbar-tray-open.toolbar-vertical.toolbar-fixed {
  margin: 0 !important;
}

.toolbar-oriented .toolbar-tray-vertical.is-active {
  width: 2.8rem;
  transition: all 0.6s;
}
.toolbar-oriented .toolbar-tray-vertical.is-active:hover {
  width: 15rem;
}

.loaded .llista-button {
  color: white;
}
.loaded #vue > header .resultat-cerca,
.loaded #vue > header .llista-button,
.loaded #no-vue > header .resultat-cerca,
.loaded #no-vue > header .llista-button {
  opacity: 1;
}
.loaded .path-stories #vue > header .resultat-cerca,
.loaded .path-stories #vue > header .llista-button,
.loaded .path-stories #no-vue > header .resultat-cerca,
.loaded .path-stories #no-vue > header .llista-button,
.loaded .page-node-type-relat #vue > header .resultat-cerca,
.loaded .page-node-type-relat #vue > header .llista-button,
.loaded .page-node-type-relat #no-vue > header .resultat-cerca,
.loaded .page-node-type-relat #no-vue > header .llista-button {
  display: none;
}

.coeli-debug-box {
  position: fixed;
  z-index: 10000;
  background: white;
  padding: 3rem;
  bottom: 0px;
  top: auto;
}

.paragraph {
  opacity: 1;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}
@media (min-width: 1024px) {
  .paragraph {
    opacity: 0;
  }
}

.paragraph.visible {
  opacity: 1;
  transform: translateY(0);
}

.page-node-type-relat main {
  background: #222;
}
.page-node-type-relat main header figure img {
  opacity: 0;
  transition: opacity 1s;
}
.page-node-type-relat main header h1 {
  opacity: 0;
  transition: opacity 1s 0.5s;
}
.page-node-type-relat.visible {
  opacity: 1;
}
.page-node-type-relat.visible main header figure img {
  opacity: 1;
}
.page-node-type-relat.visible main header h1 {
  opacity: 1;
}

.content-map-relat {
  opacity: 0;
  transition: opacity 1.8s;
}
.content-map-relat.visible {
  opacity: 1;
}
.content-map-relat.visible:nth-child(1) {
  transition: transform 0.6s 0.3s, filter 0.6s 0.3s, opacity 1s 0s;
}
.content-map-relat.visible:nth-child(2) {
  transition: transform 0.6s 0.3s, filter 0.6s 0.3s, opacity 1s 0.3s;
}
.content-map-relat.visible:nth-child(3) {
  transition: transform 0.6s 0.3s, filter 0.6s 0.3s, opacity 1s 0.6s;
}
.content-map-relat.visible:nth-child(4) {
  transition: transform 0.6s 0.3s, filter 0.6s 0.3s, opacity 1s 0.9s;
}
.content-map-relat.visible:nth-child(5) {
  transition: transform 0.6s 0.3s, filter 0.6s 0.3s, opacity 1s 1.2s;
}
.content-map-relat.visible:nth-child(6) {
  transition: transform 0.6s 0.3s, filter 0.6s 0.3s, opacity 1s 1.5s;
}
.content-map-relat.visible:nth-child(7) {
  transition: transform 0.6s 0.3s, filter 0.6s 0.3s, opacity 1s 3s;
}

#main-content.unload > * {
  opacity: 0;
  transition: opacity 0.4s ease;
}

#menu-nav .links a:hover {
  color: var(--back-corpo) !important;
}/*# sourceMappingURL=style.css.map */