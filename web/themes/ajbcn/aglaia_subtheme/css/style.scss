@import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap');

:root {
    --font-xl: 78px;
    --font-l: 3rem; //2.5vw;//36px;
    --font-m-et-plus: 2rem;
    --font-m: 1.1904789rem; //24px;
    --font-s: 1rem;
    --gap: 1.3rem;
    --back-modal: #e5e5e5;
    --back-corpo: #D93D00; //#F85C00;
    --button-action: calc(var(--header-top) * 0.6 - 2px); //5vh;
    --header-top: 20vw; //7vw;
    --filter-base: 10vh;
    --page-margin: 4vw;
    //    --page-horizontal-margin:calc(var(--header-top) * 0.25 + 11.2rem + var(--gap));
    --page-horizontal-margin: 8vw; //calc(var(--header-top) * 0.25 + 11.2rem + var(--gap));
    --deep-grey: #222;

    @media(min-width:1024px){
        --header-top: 6vw; //7vw;
        --gap: 2rem;
    }
}

// GLOBAL

* {
    box-sizing: border-box;
}
 
body,
html {
    margin: 0;
     font-size: 3vw !important;

    @media(min-width: 1024px) {
    font-size: 0.85vw !important;
    }
}

body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: "Source Sans 3", sans-serif !important;
    font-optical-sizing: auto;
    color: var(--deep-grey) !important;
    font-weight: 300;
    font-style: normal;
    transition: background-color 0.6s;

    &.oberta {
        background-color: #555;
    }

    &.mapa-loaded {
        #loading-map {
            opacity: 0;
            backdrop-filter: blur(0px);
        }
    }

    #loading-map {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        backdrop-filter: blur(5px);
        z-index: 1;
        font-size: 0;
        opacity: 1;
        transition: opacity 1s ease-in-out, backdrop-filter 1s ease-in-out;
        overflow: hidden;
        pointer-events: none;
        text-indent: -1000vw;



        &:after {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            content: "";
            display: block;
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border-radius: 50%;
            border: 4px solid #ccc;
            border-top-color: var(--back-corpo);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    }

}

.sr-only {
    //   display: none;
}

aside,
nav,
footer {
    width: 20%;
    position: relative;
    z-index: 1;
}

main {
    padding-left: calc(var(--page-horizontal-margin)*0.2); //calc(var(--page-horizontal-margin)* 2);
    padding-right: calc(var(--page-horizontal-margin)*0.2); //calc(var(--page-horizontal-margin)* 2);
    min-height: 100vh;
    background: var(--back-modal);

    @media(min-width: 1024px) {
        padding-left: calc(var(--page-horizontal-margin)*3);
        padding-right: calc(var(--page-horizontal-margin)*3);
    }
}

#vue,
#no-vue {
    >header {
        position: fixed;
        top: 1vh;
        left:0;
        border-top-right-radius: calc((var(--button-action) + 1.2rem)* 0.5);
        border-bottom-right-radius: calc((var(--button-action) + 1.2rem)* 0.5);
     //   width:83vw;
        align-items: center;
        z-index: 100;
        display: grid;
        gap: calc(var(--gap)*0.5);
        height:var(--button-action);
        width:83vw;
        padding: 0.7rem;
        padding-left:4vw;
        padding-top:0.1em;
        padding-bottom:0.1em;
        background: rgba(255, 255, 255, 1);
        backdrop-filter: blur(3px);
        //border-radius: 3px;

        @media(min-width: 1024px) {
            width:auto;
            height:auto;
            padding: 0.7rem;
            padding-right: 1em;
            top: calc(var(--header-top)*0.25 + -0.2rem);
            left: calc(var(--header-top)*0.25 + 1rem);
            border-top-right-radius: calc((var(--button-action) + 1.2rem)* 0.5);
            border-bottom-right-radius: calc((var(--button-action) + 1.2rem)* 0.5);
        }

        h1 {

            padding: 0;
            padding-right: var(--gap);
            border-right: 1px solid var(--deep-grey);
            width: min-content;


            z-index: 9;
            font-size: var(--font-m);
            font-weight: 900;
            color: var(--deep-grey);
            line-height: 0.95;
            letter-spacing: -0.06em;
            text-transform: uppercase;
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            justify-content: center;
            margin: 0;
            transition: all 0.6s;
            color: var(--back-corpo);

            span {
                font-weight: 300;
                display: none;
            }
        }

        >p {
            display: none;
        }

        .resultat-cerca {
            grid-column: 3;
            border-left: 1px solid;
            width: 9.25em;
            overflow: hidden;
            height: 100%;
            padding-left: calc(var(--gap)*0.5);
            padding-right: 0; //var(--gap);
            display: flex;
            font-weight: 400;
            align-items: center;
            font-size: var(--font-m);
            opacity: 0;
            transition: all 0.6s;

            @media(min-width:1024px){
                padding-left: var(--gap);

            width: 10em;
            }
        }

        .filtres-button {
            background-color: var(--back-corpo);
            aspect-ratio: 1;
            color: white;
            top: calc(var(--header-top)*0.25 + 1rem); //calc((4vh + 12rem )*0.5);
            //    transform: translateY(-50%);
            grid-column: 11;
            width: var(--button-action);
            border-radius: 50%;
            position: fixed;
            right: 10vw;
            z-index: 9;
        }


        .titol-pagina {
            display: block;
            text-transform: uppercase;
            color: var(--deep-grey);
            z-index: 9;
            grid-column: 2;
            grid-row: 1;
            font-weight: 900; //300;
            font-size: var(--font-m);
            margin: 0;
            line-height: 0.95;
            letter-spacing: -0.06em;
            width: 3.7em;
        }
    }
}

.menu-button {
    
    background-color:white;
    aspect-ratio: 1;
    color: white;
    top:1vh;
    grid-column: 12;
    width: var(--button-action);
    border-radius: 50%;
    position: fixed;
    right: 3vw;
    z-index: 100;

    @media(min-width: 1024px) {
        top: calc(var(--header-top) * 0.25 + 0.5rem + 1px);
        z-index: 9;
    }

    &:hover {
        cursor: pointer;
        background-color:var(--back-corpo);

        span {
            background: white;

             &:before,
        &:after{
             background: white;

        }
        }
    }

    span {
        width: 33%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: black;
        display: block;
        height: 3px;

        &:before,
        &:after {
            content: "";
            display: block;
            width: 100%;
            height: 3px;
            background: black;
            position: absolute;
            top: 0.6em;
        }

        &:before {

            top: -0.6em;
        }
    }
}

//MAP


.sr-only {
    display: none;
}

.content-map {
    // nomes per map
    perspective: 500px;
    width: 100%;
    height: 100vh;
    position: fixed;
    z-index: 0;
    top: 0;
    right: 0;
    background: #222;

    #map {
        width: 100%;
        height: 100vh;
        position: fixed;
        z-index: -1;
        top: 0;
        right: 0;
        background: #222;
        transition: all 0.6s;


    }
}

body.light {
    .content-map {
        background: #eee;

        #map,
        #map_fitxa {
            background: #eee;
        }
    }

    .clar {
        font-weight: 700;
        letter-spacing: -0.006em;
    }

    .fosc {
        font-weight: 400;
        letter-spacing: 0;
    }

    .modal-fitxa {
        box-shadow: rgba(0, 0, 0, 0.2) 0vw 0vh 20vw 3vw;

        .modal-content {
            header {

                .fitxa-titol,
                .codi {
                    color: var(--back-corpo);
                }
            }
        }

        .close-modal-button,
        .maxi-modal-button {
            background-color: var(deep-grey);
            color: white;
        }
    }

    .llista .llista-content {
        background: var(--back-modal);
        color: var(--deep-grey);

        li {
            border-bottom: 1px solid #d5d5d5;

            &:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            &.header {
                background: var(--back-modal);
            }
        }
    }
}

.color-map {

    position: fixed;
    border-bottom-left-radius: 0.8rem;
    border-bottom-right-radius: 0.8rem;
    background: var(--back-corpo);
    color: white;
    top: 100%;
    left:4vw;
    text-align: center;
    width: max-content;
    padding: calc(var(--gap)* 0.3);
    padding-left: calc(var(--gap)* 0.7);
    padding-right: calc(var(--gap)* 0.7);
    display: flex;
    text-transform: uppercase;
    font-size: 0.8em;
    font-weight: 400;
    gap: calc(var(--gap)* 0.3);
    justify-content: center;
    align-items: center;

    @media(min-width: 1024px){
    transform: translate(-50%);
    left: 50%;
    } 
        
    

    .clar,
    .fosc {
        cursor: pointer;
        transition: all 0.6s;
    }

    .fosc {
        font-weight: 700;
        letter-spacing: -0.006em;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 3.5rem;
        height: 1.4rem;

        input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--deep-grey);
            transition: 0.4s;

            &:before {
                position: absolute;
                content: "";
                height: 0.9rem;
                width: 0.9rem;
                left: 0.3rem;
                bottom: 0.3rem;
                background-color: var(--back-corpo);
                transition: 0.4s;
            }
        }

        input:checked+.slider {
            background-color: #ccc;
        }

        input:focus+.slider {
            //  box-shadow: 0 0 1px var(--deep-grey);
        }

        input:checked+.slider:before {
            transform: translateX(2rem);
            //  background-color: white
        }

        .slider.round {
            border-radius: 34px;

            &:before {
                border-radius: 50%;
            }
        }
    }
}

// MAPBOX

.mapboxgl-popup-content {

    display: flex;
    flex-direction: column;
    gap: calc(var(--gap)*0.4);
    padding: 1em !important;
    border: solid 2px var(--back-corpo);
    background: rgba(51, 51, 51, 0.8) !important;
    border-radius: 4px;
    backdrop-filter: blur(3px);


    button,
    [type="button"],
    [type="reset"],
    [type="submit"] {
        border: 0;
        padding: 0;
        background: transparent;
        color: white;
        font-size: calc(var(--font-s)*0.9);
        font-weight: 300;
        font-family: "Source Sans 3", sans-serif;
        line-height: 1.1;
        text-align: unset;

    }

    .mapboxgl-popup-close-button {
        display: none; // DAVID
        padding: 2px;
        width: 1.7em;
        transform: translate(2px, -2px);
        line-height: 1;
        color: white; //var(--back-corpo);
        aspect-ratio: 1;
        text-transform: uppercase;
        border-bottom-left-radius: 3px;

        &:hover {
            color: white;
            background: var(--back-corpo);

        }


    }
}

.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
    border-top-color: var(--back-corpo) !important;
}

.vertical {
    //   background-color: royalblue;
    transform: perspective(500px) rotateY(-0.5deg) rotateX(0deg) translateZ(0px);
    transform-origin: 100% 50%;
    opacity: 0.5;
    filter: blur(3px);
}

.horizontal {
    transform: perspective(500px) rotateY(0deg) rotateX(0.5deg) translateZ(0px);
    transform-origin: 50% 100%;
    opacity: 0.5;
    filter: blur(3px);
}

//MAP FITXA

#map_fitxa {
    width: calc(100% - (var(--gap)* 0.5));
    height: 50vh;
    position: absolute;
    top: calc(var(--gap)* 0.25);
    left: calc(var(--gap)* 0.25);
    z-index: 0;
    background: #222;
    transition: all 0.6s;
    pointer-events: none;
}

//MENU

.maxi-menu-button {
    display:none;
}
#menu-nav {
    width: 100vw;
    height: 100vh; //98vh;
    position: fixed;
    z-index: 101;
    right: 0;
    top: 0vh;
    transform: translate(100%, 0%);
    opacity: 0;
    background: white;
    transition: all 0.6s;
    padding: 2em;
    padding-top:0;

    @media(min-width:1024px){
    width: 33vw;

    }


    .close-menu-button {
        position: absolute;
        
        
        width: 3em;
        border-radius: 50%;
        background: black;
        text-indent: -100vw;
        overflow: hidden;
        aspect-ratio: 1;
        background-image: url(../img/close_b.svg);
        background-position: center;
        background-size: 40%;
        background-repeat: no-repeat;

        &:hover {
            cursor: pointer;
            background-color:var(--back-corpo);
        }
    }

    #menu-nav .close-menu-button:hover {
        color:white;
    }

    &.active {
        transform: translate(0%, 0%); //translate(-1vw, 0%);
        opacity: 1;
        box-shadow: -12vw 0 24vw rgba(0, 0, 0, 0.3);
        //    border-top-left-radius:2em;
        //    border-bottom-left-radius:2em;
    }

    .links {
        padding: 0;
        margin: 0;
       
        list-style: none;
        display: grid;
        grid-template-rows: 30% auto 1fr auto auto;
        height: 90vh;
        font-weight: 600;
        font-size: calc(var(--font-m)*1);

        a {
            color: var(--black);
        }


        li:nth-child(n+2):nth-child(-n+3) {
            font-size: var(--font-l);
            text-transform: uppercase;

        }

    }
}

// CERCA

.cercador-button {
    display: none;
    background-color: var(--deep-grey);
    color: white;
    background-image: url(../img/icon-search-w.svg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 33%;
    
    transform: translateY(-50%);
    width: var(--button-action);
    height: var(--button-action);
    //    border-radius: calc(var(--button-action)*0.5);
    position: fixed;
    z-index: 12;
    left:4px;
    top:40vh;

    @media(min-width: 1024px) {
    top: calc(var(--header-top) * 0.25 + 1rem + var(--gap) + (var(--font-m)*2)*0.95);
    left:auto;
    right: 22vw;
    }
}

.cercador {
    display: grid;
    grid-template-columns: auto auto;
    gap: calc(var(--gap)*3);
    transform: translateY(-50%);
    height: var(--button-action);
    border-radius: 0px;
    position: fixed;

    left:4vw;
    top:35vh;
    
    transition: all 0.6s;
    padding-left: 0.1vh;

    @media(min-width: 1024px) {
        left: calc(100% + calc(var(--button-action) - 2px) + (var(--gap)* 6));
        top: calc(var(--header-top) * 0.25 + 1.2rem + 1px); //calc(var(--gap) + var(--font-m)* 2* 0.95);
   
    }

    input.lupa {
        width: var(--button-action);
        height: calc(var(--button-action) - 2px);
        border: 0;
        grid-column: 3;
        aspect-ratio: 1;
        background-color: var(--back-corpo);
        border-radius: 50%;
        opacity: 0;
        transition: all 0.5s;
        background-image: url(../img/icon-search-w.svg);
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 30%;
    }

    input.text-cerca {

        grid-column: 2;

        overflow: hidden;
        height: calc(var(--button-action) - 2px); //var(--button-action);
        border: 0;
        background-color: white; //var(--back-corpo);
        font-size: 1em;
        left: 0;
        top: 0;
        opacity: 0;
        margin: 0;
        width: 0;
        ;
        padding: 0;
        padding-top: 1em;
        padding-bottom: 1em;
        transition: all 0.5s;

        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;

        &::-webkit-input-placeholder {
            color: black;
            font-size: 0.9em;
            text-transform: uppercase;
            font-weight: 700;


        }
    }

    .resultat-cerca {

        grid-column: 1;
        grid-row: 1;
        font-size: var(--font-m);
        font-weight: 700;
        height: 100%;
        background: white;
        display: flex;





        .filtrar-button {
            cursor: pointer;

            &:hover {
                text-decoration: underline;
            }

            &.obert {

                &:after {
                    content: " x";
                }

            }
        }
    }


    &.active {
        z-index: 10;
        //    width: calc(30vw);
        //    background:var(--deep-grey);
        color: black;

        input.lupa,
        input.text-cerca {
            opacity: 1;
        }

        .cercador-content {
            display: grid;
            align-items: start;
            height: 100%;

            &:hover {
                input.lupa {

                    border-top-left-radius: 0px;
                    border-bottom-left-radius: 0px;
                }

                input.text-cerca {
                    width: 17.2rem;
                    padding: 1em;
                    border-top-left-radius: calc(var(--button-action)* .5);
                    border-bottom-left-radius: calc(var(--button-action)* .5);
                }
            }
        }
    }

    #close-cerca {
        display: none;
    }

}

.llista-button {
    cursor: pointer;
    background: var(--back-corpo); //var(--deep-grey);
    color: transparent;
    //border:1px solid;
    height: calc(var(--button-action) - 2px); // calc(((var(--font-m)*2)*0.95) + 2rem);//var(--button-action);
    top: calc(var(--header-top)*0.25 + 1rem); //calc((4vh + 12rem)* 0.5);
    //    transform: translateY(-50%);
    width: calc(var(--button-action) - 2px);
    border-radius: 0px; //calc(var(--button-action)*0.5);
    padding: 0.5em;
    grid-column: 4;
    right: 10vw;
    display: flex;
    z-index: 9;
    align-items: center;
    justify-content: center;
    text-align: center;
    line-height: 1.1;
    font-size: 0.8em;
    font-weight: 400;
    text-transform: uppercase;
    transition: all 0.6s;
    border-radius: 50%;
    position:fixed;
    left: -77vw;
    top: 14vh;

    @media(min-width: 1024px) {
       position:static;
    }

    &:hover {
        background: var(--back-corpo);
    }

    a {
        color: white;
    }

}

.filtres {
    position: fixed;
    top: calc(((4vh + 12rem)* 0.5) - (var(--button-action)*0.5));
    right: 16vw;
    width: var(--button-action);
    height: var(--button-action);
    border-radius: calc(var(--button-action)*0.5);
    overflow: hidden;
    background: var(--back-modal);
    box-shadow: 0 6vh 20vh rgba(0, 0, 0, 0.5);
    transition: all 0.6s;
    z-index: 9;
    display: none;

    &.oberta {
        width: calc(80vw - 12rem - (7em*2));
        transition: all 0.6s;
    }


    &.maxi {
        top: 0;
        right: 0;
        width: 100%;
        height: 100vh;
        border-radius: 0;

        //    transform:translateX(0);
        opacity: 1;
        background: var(--back-modal);



        .maxi-filtres-button {
            background-image: url(../img/fs_m_b.svg);
        }

        .filter-box {
            opacity: 1;

        }
    }

}

.llista {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    border-radius: 0;
    opacity: 0;
    background: var(--back-modal);
    transition: all 0.6s;

    z-index: -1;

    .llista-content {
        height: calc(100%);
        position: absolute;
        top: 0;
        left: 0;
        padding-left: var(--page-horizontal-margin);
        padding-right: var(--page-horizontal-margin);
        width: 100%;
        overflow: hidden;
        padding-top: calc(4vh + 12rem);
        margin-top: 0;
        background: var(--deep-grey);
        color: white;
        transition: all 0.4s;

    }
}

.llista.maxi {
    opacity: 1 !important;
    z-index: 7;

}

//LLISTA
.llista {
    .result-list {
        list-style: none;
        margin: 0;
        padding: 0;
        padding-bottom: calc(4vh + 12rem);
        width: 100%;
        height: 100%;
        overflow: auto;

        >p {
            display: none;
        }

        li {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            border: 1px solid transparent;
            border-bottom: 1px solid #333;
            padding: 1em;
            padding-left: 0;
            padding-right: 0;
            margin: 0;
            transition: all 0.4s;

            p {
                margin: 0;
            }

            .identifier {
                color: var(--back-corpo);
                display: flex;
                justify-content: space-between;

                span {
                    //    border-left:1px solid;
                    font-weight: 700;
                    //    margin-left:calc(var(--gap)*0.5);
                    //    padding-left:calc(var(--gap)*0.5);
                    flex-basis: 70%;
                    //     text-align: right;

                }

            }

            .districts {
                text-align: end;
            }

            &:hover {
                cursor: pointer;
                background: rgba(0, 0, 0, 0.3);
                padding-left: 1em;
                padding-right: 1em;
            }

            &.header {
                font-weight: 900;
                position: sticky;
                top: 0;
                background: var(--deep-grey);

                &:hover {
                    background-color: inherit;
                    padding-left: 0;
                    padding-right: 0;
                }
            }
        }
    }

    .view-more {
        button {
            all: unset;
            background-color: var(--back-corpo);
            width: calc(var(--button-action) - 2px);
            aspect-ratio: 1;
            text-indent: -100vw;
            background-image: url(../img/more_b.svg);
            background-size: 30%;
            margin: auto;
            margin-top: calc(var(--gap)*2);
            ;
            background-repeat: no-repeat;
            background-position: center center;
            ;
            overflow: hidden;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0.5rem;
            font-size: 0.9em;
            font-weight: 400;
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.6s;
            border: 0;
            border-radius: 100%;
            box-sizing: border-box;
        }
    }
}

.navegacio {
    display: flex;
    list-style: none;
    gap: var(--gap);
    justify-content: center;
    margin-top: 3em;

    .actiu {
        font-weight: 700;
    }

    a {
        color: black;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
            font-weight: 600;
        }
    }
}

//FILTRES {
.filtres {


    .filtres-content {
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        padding-top: calc((4vh + 12rem));
        padding-left: calc((12rem + 4vw));
        width: 100%;
    }


    .filter-box {
        height: calc(100% - var(--button-action) - (var(--gap) * 4));
        opacity: 0;
        transition: all 0.6s 0.6s;

        form {
            height: 100%;
        }

        .filter-list {
            display: flex;
            gap: calc(var(--gap)*2);
            flex-wrap: wrap;
            list-style: none;
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: auto;

            >li {
                //    border-left:1px solid black;
                //    padding-left:var(--gap);
                height: 100%;
                overflow: auto;


                .filter-type-title {
                    font-weight: 600;
                }

                .sub-list {
                    ul {
                        list-style: none;
                        margin: 0;
                        padding: 0;

                        li {
                            input {
                                width: 1px;
                                height: 1px;
                                opacity: 0.0001;
                                border: 0;
                                position: absolute;

                                &[disabled]+label {
                                    opacity: 0.3;
                                }

                                &[type="checkbox"]:checked:disabled+label {
                                    opacity: 0.7;
                                }

                                &[type="checkbox"]:checked+label {
                                    opacity: 1;
                                    font-weight: 600;
                                }
                            }
                        }
                    }
                }
            }
        }

        .filter-base {
            display: flex;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            align-items: center;
            justify-content: space-evenly;
            padding: calc(var(--gap)*2);

            .filter-button-empty,
            .filter-button-use,
            .filter-total-value {
                >div {
                    font-weight: 700;
                    font-size: 0.5rem;
                    text-transform: uppercase;
                    display: flex;
                    border: 0;
                    align-items: center;
                    justify-content: center;
                    height: var(--button-action);
                }
            }

            .filter-button-use .executar-filtre {
                color: white;
                background: var(--back-corpo);
                aspect-ratio: 1;
                border-radius: calc(var(--button-action)*0.5);
                opacity: 0;
                text-align: center;
                transition: all 0.6s;

                &.obert {
                    opacity: 1;
                }

            }

            .filter-total-value {

                padding: 0;
                line-height: var(--button-action);
                color: black;
                width: fit-content;
                background: transparent;
                font-weight: 700;
                font-size: 0.7rem;
                text-transform: uppercase;

            }

            .filter-button-empty>div {

                color: white;
                background: black;
                aspect-ratio: 1;
                border-radius: calc(var(--button-action)*0.5);
                text-align: center;
                font-size: 0.5rem;
                text-transform: uppercase;

            }

        }

    }



    .maxi-filtres-button {
        background-image: url(../img/fs_x_b.svg);
        background-position: center;
        background-size: 40%;
        background-repeat: no-repeat;
        display: block;
        aspect-ratio: 1;
        position: absolute;
        top: 1em;
        right: 3.8em;
        width: 2em;
        border-radius: 50%;
        background-color: black;
        text-indent: -100vw;
        overflow: hidden;
        z-index: 2;

    }


    .close-filtres-button {
        position: absolute;
        top: 1em;
        right: 1em;
        width: 2em;
        border-radius: 50%;
        background: black;
        text-indent: -100vw;
        overflow: hidden;
        aspect-ratio: 1;
        background-image: url(../img/close_b.svg);
        background-position: center;
        background-size: 40%;
        background-repeat: no-repeat;
        z-index: 2;
    }

    //LISTA FILTRES 

}

// MODAL
.modal-fitxa {
    position: fixed;
    overflow: hidden;
    top: 100vh;
    left: 50%;
    transform: translateX(-50%);
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0vw 0vh 20vw 3vw rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    //    border-radius:2em;
    transition: all 0.6s;
    z-index: 100;
    opacity: 0;

    @media(min-width: 1024px) {
    width: calc(100vw - (var(--page-horizontal-margin)*6));
        height: calc(100vh - (var(--header-top) * 0.25 + 1rem));
        
    }


    .modal-content {
        overflow: auto;
        height: 100%;
        scroll-behavior: smooth;
        position: absolute;
        top: 0;
        left: 0;
        background: white;
        padding-left: 0;
        width: 100%;
        //    padding-left:2em;
        transition: all 0.6s;

        a {
            text-decoration: none;
            color: black;
            transition: all 0.6s;
        }


        header {
            position: sticky;
            top: 1.7rem; //2.8rem;//-1.5rem;//3.5em;
            margin-top: 25vh; //30vh;
            z-index: 1;
            transition: all 0.6s;
            min-height: calc(((var(--font-l)* 1.5)* 1.1) + var(--font-l));
            display: flex;
            //transition: all 0.6s;
            text-transform: none;
            flex-direction: column;
            justify-content: flex-end;
            padding-left: 2rem;
            height: auto;
            //    padding-right:20%;
            width: 30em;
            padding-bottom: 2rem;
            color: white;
            padding-right: 0;

            .header-content {
                min-height: calc(var(--font-m-et-plus) * 6.6); //calc(var(--font-l)*3.3);    
                display: flex;
                flex-direction: column;
                //    justify-content: flex-end;

            }


            .fitxa-titol {
                font-size: var(--font-m-et-plus); //calc(var(--font-l));
                font-weight: 600;
                margin: 0;
                line-height: 1.1;
                transition: all 0.6s;
                text-transform: none;
                color: inherit;

            }

            .codi {
                font-size: var(--font-m-et-plus); //calc(var(--font-l));
                transition: all 0.6s;
                margin: 0;
                line-height: 1.1;
                color: inherit;
            }

            &.sticky {
                color: var(--back-corpo);
                width:19em;
                 pointer-events: none;

                .fitxa-titol , .codi{
                    font-size:var(--font-m);
                }
            }
        }

        article {
            background: white;
            position: relative;
            z-index: 0; // DAVID
            padding-left: 2rem;
            display: flex;
            overflow: visible;
            align-items: flex-start;
            gap: 2rem;
            padding-right: 0;

        }

        aside {
            position: sticky;
            flex-basis: 30%;
            top: calc(3.5vh + (var(--font-l)* 0.5) + 4rem); //calc((var(--font-l)*1.5) + (var(--font-l)));
            margin-top: 15vh;
            transition: all 0.6s;
            font-size: var(--font-s);
            line-height: 1.3;
            text-transform: uppercase;
            padding-right: var(--gap);
        }

        .contingut_modal {
            flex-basis: 70%;
        }

        .bloc {
            padding-top: calc(3.5vh + (var(--font-l)* 0.5) + 4rem);
            //    padding-left:30%;
            padding-right: 20%;
            font-size: var(--font-s);
            line-height: 1.4;


            &:first-child {
                padding-top:15vh
            }

            &#documentacio {

                padding-bottom: 50vh;

                ul {
                    li {
                        color: transparent;
                        display: flex;
                        flex-direction: row-reverse;
                        justify-content: flex-end;

                        a {
                            width: calc(var(--button-action)* 2);
                            aspect-ratio: 0.77;
                            position: relative;
                            height: auto;
                            display: inline-block;
                            border: 1px solid black;
                            font-size: 1.5em;
                            padding: 0.5em;
                            margin-bottom: var(--gap);
                            display: inline-flex;
                            justify-content: center;
                            font-weight: 600;
                            align-items: center;
                            line-height: 1;
                            color: grey;
                            //    margin-left: var(--gap);
                            overflow: hidden;

                            &:after {
                                content: "PDF";
                                display: flex;
                                align-items: center;
                                font-size: 0.5em;
                                padding: 1em;
                                font-weight: 700;
                                aspect-ratio: 1;
                                background: red;
                                color: white;
                                transform: rotate(45 deg);
                                position: absolute;
                                top: 0;
                                left: 0;

                            }

                            &:hover {
                                color: white;
                                background: var(--deep-grey);
                            }
                        }
                    }
                }

                .alerta-cc {
                    max-width: 50%;

                    img {
                        margin-right: var(--gap);
                    }
                }

                dl {
                    margin-bottom: 4rem;

                }
            }

            h3 {
                font-size: calc(var(--font-s) * 0.8);
                font-weight: 600;
                text-transform: uppercase;
                color: var(--back-corpo);
                margin-bottom: 0em;
                margin-top: 2em;

                &:first-child {
                    margin-top: 0;
                }
            }
        }

        .menu_modal {
            margin: 0;
            padding: 0;
            list-style: none;

            li {
                transition: all 0.3s;

                &:hover {
                    font-weight: 700;
                }

                &.active {
                    font-weight: 700;

                    a {
                        color: var(--back-corpo);
                    }
                }
            }
        }

        dl {
            margin: 0;
            padding-top: 1rem;
            margin-bottom: 2rem;
            display: grid;
            grid-template-columns: 1fr 3fr;
            transition: all 0.6s;
            border-left: 1px solid var(--back-corpo);
            padding-left: calc(var(--gap)*0.66);
            font-size: calc(var(--font-s) * 0.8);

            dt {
                grid-column: 1;
                margin-bottom: 0.5em;
            }

            dd {
                grid-column: 2;
                font-weight: 600;

                &.bloc-any {
                    border-top: 1px solid #ddd;
                    padding-top: 1em;
                    margin-top: 1em;

                    &:first-of-type {
                        border-top: 0;
                        padding-top: 0;
                        margin-top: 0;
                    }
                }
            }

            ;

            &.full,
            &.resultats {
                dd {
                    grid-column: 1/span 2;
                    margin-left: 0;
                    margin-bottom: 1em;
                    font-weight: inherit;

                    br,
                    .line-break {
                        display: block;
                        margin-top: 1em;
                        margin-bottom: 1em;
                    }
                }
            }
        }

        .content-map {

            #map_fitxa {
                height: calc((25vh + var(--font-m-et-plus) * 6.6 + 2rem) - (var(--gap)*0.5)); //calc(30vh + (var(--font-l)* 3.3) + 2rem);
            }
        }

        #informacio_tecnica_i_legal dd:has(> *:contains("Any:")),
        #informacio_tecnica_i_legal dd:has(> :contains("Any:")),
        #informacio_tecnica_i_legal dd:has(> span:contains("Any:")) {
            border-top: 1px solid;
            padding-top: var(--gap);
        }
    }

    .maxi-modal-button {
        background-image: url(../img/fs_x_b.svg);
        background-position: center;
        background-size: 24%;
        border-radius: 50%;
        background-repeat: no-repeat;
        display: none;
        aspect-ratio: 1;
        position: absolute;
        top: calc(var(--gap) + var(--font-m) * 2 * 0.95 - ((var(--header-top) * 0.5 + 2rem) * 0.5));
        width: calc((var(--button-action) - 2px)*.7);
        right: calc(var(--button-action) - 2px + 2em);
        background-color: var(--back-corpo);
        text-indent: -100vw;
        overflow: hidden;
        z-index: 2;
        transition: all 0.6s;

        @media(min-width: 1024px) {
            display:block;
        }

        &:hover {
            background-color: var(--back-corpo);
        }
    }

    &.oberta {
        top:0;
        opacity: 1;
       

        @media(min-width: 1024px) {
            top: calc((var(--header-top) * 0.25 + 1rem)*0.5);
        }
    }

    &.maxi {
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        transform: translateX(0);
        border-radius: 0;
        background: rgba(255, 255, 255, 1);

        header {
            //    transform:translateX(-26em);
            width: 30%;
            top: 12em;
            width: 30%;

            .fitxa-titol,
            .codi {
                font-size: calc(var(--font-l)*1.2);
            }
        }

        .modal-content {
            padding-top: 1vh;
            //    padding-left: 30em;

            aside {
                //    transform:translateX(-26em);
                top: calc((var(--font-m-et-plus) * 0.5) + 16em);
            }

            dl {
                font-size: calc(var(--font-s) * 1.1);
            }

            .bloc {
                padding-top: calc(var(--font-m-et-plus) * 0.5 + 16em);
            }

            .content-map #map_fitxa {
                height: calc(25vh + var(--font-m-et-plus) * 6.6 + 3rem - var(--gap) * 0.5);
            }
            header.sticky {
                top: 1.7rem;
                .fitxa-titol,.codi {
                font-size: calc(var(--font-l) * 1.2);

            }
        }
        }

        .maxi-modal-button {
            background-image: url(../img/fs_m_b.svg);
        }
    }

    .close-modal-button {
        position: absolute;
        top: calc(var(--gap) + var(--font-m) * 2 * 0.95 - ((var(--header-top) * 0.5 + 2rem) * 0.5));
        right: 1em;
        width: calc((var(--button-action) - 2px)*.7);
        border-radius: 50%;
        background-color: var(--back-corpo);
        text-indent: -100vw;
        overflow: hidden;
        aspect-ratio: 1;
        background-image: url(../img/close_b.svg);
        background-position: center;
        background-size: 23%;
        background-repeat: no-repeat;
        z-index: 2;
        transition: all 0.6s;

        &:hover {
            background-color: var(--back-corpo);
        }
    }

}

.content.paraules {
    padding-top: calc(4vh + 12rem);
    padding-left: calc(12rem + 4vw);
    padding-right: 10vw;
}

/** afegit dani **/
/** paginacio llista */

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.pagination button {
    margin: 0 10px;
    padding: 5px 10px;
}

//COELI

//#block-coeli-map-theme-content

.path-mapa {
    .coeli-search-filters {
        display: block;
        opacity: 1;

        .coeli-result-filter-box {
            position: fixed;
            top: 0; //calc((4vh + 12rem)* 0.5);
            right: 0; //16vw;
            left: auto;
            padding: 0;
            transform: translateY(-100%); //translateY(-50%);
            width: 100%; //calc(80vw - 12rem - 14em);
            height: var(--button-action);
            border-radius: 0px; //calc(var(--button-action)* 0.5);
            opacity: 1;
            z-index: 10;
            background-color: #2220;
            backdrop-filter: blur(0px);
            transition: all 1.2s 0.6s;
            box-shadow: 0vh 0 0vh rgba(0, 0, 0, 0.5);

            &:before {
                display: none;
            }

            >* {
                opacity: 0;
                transition: opacity 0.6s 0s;
            }

            .coeli-filter-list {
                display: grid;
                grid-template-columns: repeat(8, 1fr);
                height: 100%;


                .coeli-filter-list-group {
                    position: relative;
                    width: calc(((100vw - (var(--page-margin)*2)) - ((var(--gap)* 1)* 7)) / 8);
                    max-height: calc(100vh - (6rem + ((var(--button-action) + 2rem - 2px) + (var(--button-action)*1.5))));


                    &:before {
                        display: block;
                        content: "";
                        background-color: #555;
                        width: 1px;
                        height: 100%;
                        position: absolute;
                        left: calc(var(--gap) *-0.5);
                    }

                    &:first-child {
                        &:before {
                            display: none;
                        }
                    }

                    .coeli-filter-type-title {
                        padding: 0;
                        margin: 0;
                        margin-bottom: 1rem;
                        height: 2rem;
                        line-height: 2rem;
                        border-bottom: 1px solid #555;
                        color: white;
                        font-size: 0.9em;
                        text-transform: uppercase;

                    }

                    .coeli-sub-list {
                        height: auto;
                        overflow: visible;

                        ul {
                            position: absolute;
                            top: 0;
                            display: flex;
                            flex-direction: column;
                            flex-wrap: nowrap;
                            max-height: calc(98vh - (9rem + (var(--filter-base) + (var(--button-action)*1.5))));
                            overflow: auto;
                            width: 100%;

                            li {
                                display: block;
                                padding: 0;
                                margin: 0;


                                &:first-child .term-label {
                                    border: 0;
                                    margin-top: 0;
                                    padding-top: 0;
                                }

                                &:has(input[type=checkbox]:checked) {
                                    //   order: initial!important;//order: -1;
                                    margin-bottom: 1px;

                                    &+label:after {
                                        display: none;
                                    }
                                }

                                .term-label {
                                    color: white;
                                    border-top: 1px dotted #555;
                                    display: block;
                                    padding-top: 1em;
                                    width: calc(100% - 0.3em);
                                    margin-top: 1em;
                                    padding-bottom: 0.5em;


                                }

                                label {
                                    margin: 0;
                                    padding: 0.2em;
                                    border-radius: 0;
                                    color: white;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: flex-start;

                                    &:hover {
                                        background: var(--back-corpo);
                                    }
                                }

                                [type=checkbox]:disabled+label {
                                    //    display:none;
                                }

                                [type=checkbox]:checked+label {
                                    background: var(--back-corpo);

                                    &:after {
                                        display: none;
                                    }

                                }

                            }

                            li[class*="Area-funeraria"],
                            li[class*="Area-productiva_Agricola"],
                            li[class*="Oci-i-Espectacles"],
                            li[class*="Sistema-hidraulic"],
                            li[class*="Obra-publica-i-civil"],
                            li[class*="Material-descontextualitzat_"],
                            li[class*="Higiene-i-Salut"],
                            li[class*="Habitat"],
                            li[class*="Estructures-defensives-i-militars"],
                            li[class*="Estratigrafia"],
                            li[class*="Edifici-de-culte-o-religios"],
                            li[class*="Edifici-administratiu"],
                            li[class*="Bens-Immobles-aillats"],
                            li[class*="Material-descontextualitzat"],
                            li[class*="Area-productiva"] {

                                order: 1;
                                padding-left: 10px !important;

                                &.Patrimoni-Immoble {
                                    padding-left: 0px !important;
                                    border-bottom: 1px dotted white;
                                    display: block !important;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;
                                }
                            }

                            .Patrimoni-Immoble {
                                display: none !important;
                            }

                            li[class*="Patrimoni-Moble-Aillat_"] {
                                padding-left: 10px !important;
                                border-bottom: 0px solid white;
                                padding-top: 0em !important;
                                margin-bottom: 0 !important;
                            }

                            .Patrimoni-Moble-Aillat {
                                order: 2;
                                border-bottom: 1px dotted white;
                                padding-top: 1em !important;
                                margin-bottom: 0.5em !important;
                                padding-bottom: 0.2em !important;
                            }


                            li[class*="Patrimoni-Natural_"] {
                                border-bottom: 0px solid white;
                                padding: 0em !important;
                                margin-bottom: 0 !important;
                                padding-left: 10px !important;
                            }


                            .Patrimoni-Natural {
                                order: 2;
                                border-bottom: 1px dotted white;
                                padding-top: 1em !important;
                                margin-bottom: 0.5em !important;
                                padding-bottom: 0.2em !important;


                            }

                            // CRONOLOGIA




                            .Preurba {
                                &_Prehistoria {
                                    order: 1;
                                    border-bottom: 1px dotted white;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;
                                }

                                &_Protohistoria {
                                    order: 3;
                                    border-bottom: 1px dotted white;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;
                                }

                            }

                            .Prehistoria,
                            .Neolitic,
                            .Bronze,
                            .Mig\.-4000---3300,
                            .Calcolitic\.-2900 {
                                order: 2;
                                margin-left: 10px !important;
                            }

                            .Protohistoria {
                                order: 4;
                                margin-left: 10px !important;
                            }

                            .Preurba_218---10-a\.C\. {
                                order: 5;
                                margin-left: 10px !important;
                                border: none;

                            }


                            .Urba {
                                &_Contemporani-\(Metropolita\) {
                                    order: 10;
                                    margin-left: 10px !important;

                                }

                                &_Roma-imperi {
                                    order: 9;
                                    border-bottom: 1px dotted white;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;

                                }

                                &_Antiguitat-tardana {
                                    order: 11;
                                    border-bottom: 1px dotted white;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;
                                }

                                &_Medieval,
                                &_Predomini-del-Consell-de-Cent {
                                    order: 13;
                                    border-bottom: 1px dotted white;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;

                                }

                                &_Modern {
                                    order: 15;
                                    border-bottom: 1px dotted white;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;

                                }

                                &_Contemporani {
                                    order: 19;
                                    border-bottom: 1px dotted white;
                                    padding-top: 1em !important;
                                    margin-bottom: 0.5em !important;
                                    padding-bottom: 0.2em !important;

                                }
                            }

                            .Roma-imperi {
                                order: 10;
                                margin-left: 10px !important;
                            }

                            .Antiguitat-tardana {
                                order: 12;
                                margin-left: 10px !important;
                            }

                            .Medieval,
                            .Predomini-del-Consell-de-Cent {
                                order: 14;
                                margin-left: 10px !important;
                            }

                            .Modern {
                                order: 16;
                                margin-left: 10px !important;
                            }

                            ._Contemporani-\(Metropolita\) {
                                order: 17;
                            }

                            .Contemporani {
                                order: 18;
                                margin-left: 10px !important;
                            }

                            .Contemporani-\(Metropolita\) {

                                order: 20;
                                margin-left: 10px !important;
                            }

                            ._Preurba,
                            ._Urba {
                                display: none !important;
                            }
                        }
                    }
                }
            }

            .coeli-filter-base {
                background: transparent !important;
                backdrop-filter: blur(4px);
                //    border-top:1pX solid #ccc;
                grid-template-columns: auto 10vw auto;
                filter: none;
                gap: var(--gap);
                height: calc(var(--button-action) + 2rem - 2px);
                padding: 1rem;

                .coeli-filter-total-value {
                    color: white;
                    text-transform: uppercase;
                    font-size: 0.9rem;
                    font-weight: 700;
                    flex-direction: row;
                    gap: calc(var(--gap)*0.5);



                    @media(min-width: 1024px) {
                        grid-column: 1;
                        justify-self: flex-end;
                    }

                    &.coeli-loading-filtres i {
                        left: 0;
                        transform: translate(-160%, -50%);
                    }
                }

                .coeli-filter-button-empty {

                    @media(min-width: 1024px) {
                        grid-column: 3;
                        justify-self: inherit;
                    }

                    button {
                        color: white;
                        text-transform: uppercase;
                        font-size: 0.9rem;
                        font-weight: 700;
                        cursor: pointer;
                        //    border:1px solid #fff9;
                        height: calc(var(--button-action) - 2px);
                        border-radius: 0;


                        &:before {
                            //   background-image: url(../images/close_b.svg);
                        }
                    }
                }

                .coeli-filter-button-use {
                    @media(min-width: 1024px) {
                        grid-column: 2;
                        justify-self: inherit;

                        button {
                            background: var(--back-corpo);
                            color: white;
                            height: calc(var(--button-action) - 2px);
                            border-radius: calc((var(--button-action) - 2px)* 0.5);
                            font-size: 0.9rem;
                            margin: 0;
                            text-transform: uppercase;
                            font-weight: 700;
                            cursor: pointer;
                        }

                    }

                }
            }

        }

        &.active-filters {

            z-index: -1;

            .coeli-result-filter-box {
                position: fixed;
                color: #000;
                width: 100vw;
                transform: translateY(0);
                box-shadow: 5vh 0 20vh rgba(0, 0, 0, 0.5);
                top: 0;
                right: 0;
                font-weight: 600;
                height: 100vh;
                background: #222d;
                backdrop-filter: blur(4px);
                overflow: auto;
                padding: var(--page-margin);
                padding-top: calc(((4vh + 12rem)* 0.5) + var(--button-action));
                padding-bottom: calc(var(--button-action) + 2rem - 2px);
                opacity: 1;
                z-index: 11;
                border-radius: 0;

                >* {
                    opacity: 1;
                    transition: opacity 0.6s 1.2s;
                }
            }

        }
    }

    .coeli-list-results {
        background: none;




        .coeli-selected-facets {
            display: flex;
            width: 26vw;
            align-items: flex-end;
            align-content: flex-end;
            flex-wrap: wrap;
            gap: 0.2em;
            min-height: 100%;

            a {
                background: var(--back-corpo);
                border-radius: 2px;
                margin-bottom: 0;
                margin-right: 0;
                text-decoration: none;
                font-size: 0.9em;
                line-height: 0.85;
            }
        }
    }

    .coeli-result-filter-box-button {
        height: 100%;
    }

    .open-close-button {
        height: 100%;
        outline: none !important;
        box-shadow: none !important;
        border-color: inherit !important;
        cursor: pointer;
        color: white;
        font-size: 0.8em;
        transition: all 0.6s;

        &:after,
        &:before {
            content: "";
            display: block;
            width: 0%;
            height: 0px;
            background: transparent;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(0deg);
            transition: all 0.6s;
        }

        &:after {
            transform: translate(-50%, -50%) rotate(0deg);
        }
    }

    &.active-filters {

        .open-close-button {
            height: 100%;
            overflow: hidden;
            color: transparent;
            outline: none !important;
            box-shadow: none !important;
            border-color: inherit !important;


            //    position:relative;

            &:after,
            &:before {

                width: 40%;
                height: 2px;
                background: white;
                transform: translate(-50%, -50%) rotate(45deg);
            }

            &:after {
                transform: translate(-50%, -50%) rotate(-45deg);
            }
        }


    }
}

.filtrar-button {
    position: fixed;
    border-radius: 0px;
    transform: translateY(-50%);
    border: 0;
    
    background-color: var(--back-corpo); //var(--deep-grey);
    height: calc(var(--button-action) - 2px); //calc(((var(--font-m)*4)*0.95) + 2rem);//var(--button-action);
    width: calc(var(--button-action) - 2px);
    overflow: hidden;
    color: white;
    display: flex;
    z-index: 11;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 0.9em;
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.6s;
    border-radius: 50%;
    left: 4vw;
    top: 26vh;

    @media(min-width: 1024px) {
        top: calc(var(--header-top) * 0.25 + 1.2rem + 1px); //calc( var(--gap) + (var(--font-m)*2)*0.95);//calc(var(--header-top)*0.25 + 1rem);//calc((4vh + 12rem)* 0.5);
        left: calc(100% + (var(--gap)* 3));
    }

    button {
        background: none;
        border: 0;
        text-transform: uppercase;
        margin: 0;
        font-weight: 400;
        padding: 0;

    }

    &:hover {
        background-color: var(--back-corpo);
    }
}

.coeli-close-filters {
    display: none;
}

#loading {
    display: none !important;
}

// CORPO

.corpo {
    background-color: rgba(255, 255, 255, 0.8);
    position: fixed;
    border-radius: 0px;
    bottom: 0;
    left: 0;
    width:100%;
    padding: 0.7em;
    font-size: calc(var(--font-s)*0.9);
    z-index: 10;
    font-weight: 300;

    @media (min-width: 1024px) {
        width:auto;
       bottom: var(--gap);
    left: calc(var(--header-top)* 0.25 + 1rem);
    }

    a {
        color: var(--back-corpo);
        text-decoration: none;

        &:hover {
            text-decoration: underline;
            color: var(--back-corpo);
        }
    }


}

//PUBLICACIONS
.path-stories {
    .titol-pagina {
        a {
            text-decoration:none!important;
            color:black!important;
            pointer-events: none;
        }
    }

}
.path-stories,
.page-node-type-relat {

    background: #222; //var(--back-modal);

    #vue,
    #no-vue {
        >header {

            .resultat-cerca,
            .llista-button {
                display: none;
            }

            .titol-pagina {
                width: max-content;
                padding-right: calc(var(--gap)*0.5);

                @media(min-width:1024px){
                    padding-right: var(--gap);
                }

                a {
                    color: var(--back-corpo);
                }
            }
        }
    }

    main {
        background: white; //var(--deep-grey);
    }

    .filtrar-button,
    .cercador,
    #block-coeli-map-theme-content .coeli-list-results {
        display: none;
    }

    .relats-list {


        ul {
            margin: 0;
            padding: 0;
            padding-top: 10vh;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--gap);
        }

        li {
            background: linear-gradient(120deg, #d96c00, #ae2f00);
            margin: 0;
            padding: 0;
            filter: drop-shadow(0 0rem 0rem #0000);
            transition: all 0.6s;

            &:hover {
                transform: translateY(-1rem);
                filter: drop-shadow(0 1.5rem 2rem #000);

                a.veure-fitxa {
                    transform: translateY(-0.3rem);
                    background: white;
                    color: var(--back-corpo);
                    filter: drop-shadow(0 0.9rem 0.9rem rgba(0, 0, 0, 0.4));
                }
            }


            h2 {
                font-size: calc(var(--font-l)*0.4);
                font-weight: 700;
                margin: 0;
                line-height: 1.1;
                color: var(--back-modal);
                //    max-width:50%;
                text-transform: none;
                padding-left: 1rem;
                padding-top: 1rem;
                padding-right: 1rem;

                @media(min-width:1024px){
                    font-size: calc(var(--font-l)*0.7);
                padding-left: 2rem;
                padding-top: 2rem;
                padding-right: 2rem;
                }
            }

            p {
                font-size: calc(var(--font-m)*0.9);
                font-weight: 300;
                color: var(--back-modal);
                 padding-left: 1rem;
                padding-top: 1rem;
                padding-right: 1rem;

                @media(min-width:1024px){
                padding-left: 2rem;
                padding-top: 2rem;
                padding-right: 2rem;
                }

                &:last-child {
                    padding-bottom: 1rem;

                    @media(min-width:1024px){
                        padding-bottom: 2rem;
                    }
                }

            }

            a {
                color: white;
                text-decoration: none;
            }

            a.veure-fitxa {
                color: white;
                text-decoration: none;
                border: 1px solid;
                padding-left: 2em;
                padding-right: 2em;
                line-height: 2em;
                display: inline-block;
                border-radius: 0.2em;
                font-weight: 600;
                transition: all 0.6s 0.3s;
            }

            .imatge {
                background: #c2c2c2;
                width: 100%;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    display: block;
                }
            }

            &.col-1 {
                .imatge {
                    width: 100%;
                    aspect-ratio: 1.6;
                } 
            }

            &.col-2 {
                grid-column: span 2;
                display: grid;
                max-width: 100%;
                grid-template-columns: repeat(2, 1fr);
                grid-template-rows: 1fr auto auto;
                gap: var(--gap);
                row-gap: 0;

                .imatge {
                    grid-column: span 1;
                    grid-row: span 3;
                    aspect-ratio: 1;
                }

                h2 {
                    grid-column: 2;
                    grid-row: 1;

                }

                P {
                    grid-column: 2;

                    &:last-child {
                        grid-column: 2;
                    }
                }

                &.full-img {

                    .imatge {
                        grid-column: span 2;
                        grid-row: 1;
                        aspect-ratio: 2;
                        grid-row: 1;
                    }

                    h2 {
                        grid-column: span 2;
                        grid-row: 2;
                    }

                    p {
                        grid-column: span 2;
                        grid-row: 3;

                        &:last-child {
                            grid-column: span 2;
                            grid-row: 3;
                        }
                    } 
                }
            }
        }
    }

    main {
        margin-bottom: 10vh;
    }
}
nav.pager {
    width:100%;

    .pager__items {
        margin-top:4rem;
        border-top:2px solid white;
        padding-top:1rem;
        width:100%;
        display:flex;
        justify-content: center;
        position:relative;

        a {
            color:white;
            font-size:var(--font-m);
        }
        .pager__item--previous {
            position: absolute;
            left:0;
        }
        .pager__item--next {
            position: absolute;
            right:0;
        }
        .pager__item--first, .pager__item--last {
            display:none;
        }
    }
}
 
.path-stories {
    main {
        background: #222;
    }
}

.page-node-type-relat {


    .content-map-relat {
        position: fixed;
        width: 100%;
        height: 100vh;
        top: 0;
        left: 0;
        z-index: -1;

        .mapboxgl-map {
            width: 100%;
            height: 100%;

            .mapboxgl-canvas-container {
                width: 100%;
                height: 100%;
            }
        }
    }

    main {
        margin-bottom: 80vh;
        //    padding-bottom: 20vh;
        padding-left: 0;
        padding-right: 0;

        header {
            //    margin-left:calc(var(--page-horizontal-margin)*-3);
            padding-left: calc(var(--page-horizontal-margin)*0.5);
            padding-right: calc(var(--page-horizontal-margin)*0.5);
            width: 100vw;
            height: 100vh;
            position: relative;
            z-index: 1;

            @media(min-width:1024px){
                padding-left: calc(var(--page-horizontal-margin)*3);
                padding-right: calc(var(--page-horizontal-margin)*3);
            }

            h1 {
                margin: 0;
                font-size: calc(var(--font-l)* 0.75);
                color: white;
                line-height:1.2;
                background: linear-gradient(120deg, #d96c00, #ae2f00);
                padding: 2rem;
                position: absolute;
                bottom: 5rem;
                left: calc(var(--page-horizontal-margin)*0.5);
                right: calc(var(--page-horizontal-margin)*0.5);

                @media(min-width:1024px){
                left: calc(var(--page-horizontal-margin)*3);
                right: calc(var(--page-horizontal-margin)*3);
                font-size: calc(var(--font-l)* 1.5);
                bottom: 5rem;
                }
            }

            figure {
                position: absolute;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: #222; //#c2c2c2;
                margin: 0;
                z-index: -1;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        article,
        .body-content {
            padding-top: 6rem;
            background: white;
            position: relative;
            padding: 6rem;
            font-size: calc(var(--font-m)*1.2);
            padding-left: calc(var(--page-horizontal-margin)*0.5);
            padding-right: calc(var(--page-horizontal-margin)*0.5);

            @media(min-width:1024px){
            padding: 6rem;
                padding-left: calc(var(--page-horizontal-margin)*3);
                padding-right: calc(var(--page-horizontal-margin)*3);
            }

            .paragraph {
                background: transparent !important;
                font-size: calc(var(--font-m));
                padding: 0;

                .paragraph-title {
                    h2 {
                        font-size: var(--font-m);
                        text-transform: none;
                        font-weight: 700;
                        margin-bottom: 1rem;
                        margin-top: 4rem;
                        text-align: left;
                    }
                }

                .pager-loadmore {
                    display: none !important;
                }

                 &.paragraph--type--paragraph-galeria {
                    padding-top:4rem;
                    padding-bottom:4rem;
                    .info {
                        display:none!important;
                    }
                    .media-list {
                        width:100%!important;
                        height: auto !important;
                        aspect-ratio:1;

                        @media(min-width:1024px){
                            aspect-ratio:16/9;
                        }

                        .media-video, .media-img {
                            width:100%;
                            height:100%;
                            opacity:1;
                            pointer-events: none;
                            aspect-ratio:1;

                            @media(min-width:1024px){
                                aspect-ratio:16/9;
                            }
                        }
                    }
                }  
            }


            h2 {}

            p {
                line-height: 1.6;
                margin-top: 2rem;
            }

            figure {
                margin: 0;
                padding: 0;
                margin-bottom: 4rem;

                img {
                    width: 100%;
                    height: auto;
                    max-height: 30vh;
                }

                figcaption {
                    margin-top: 1rem;
                    font-size: 0.8em;
                }
            }

            iframe {
                width: 100% !important;
                height: auto !important;
                aspect-ratio: 1.77;
                overflow: hidden;
            }

        }
    }
}
.path-frontpage {
   background:var(--back-modal);

   .paragraph--type--paragraph-intro .field-paragraph-intro-text {
    a {
        display:block;
        aspect-ratio:1;
        display:flex;
        border-radius:50%;
        width:6rem;
        color:white;
        background-color:var(--back-corpo);
            align-items: center;
    justify-content: center;
    text-decoration:none;
    text-transform:uppercase;
    span {
    font-weight:700!important;
 }
    &:hover {
        color:white;
    }
    }
   }
}
.path-node.page-node-type-pagina-basica {
    #vue > header{
    /*    .llista-button, .titol-pagina {
            cursor: pointer;
            background: var(--back-corpo);
            color: transparent;
            height: calc(var(--button-action) - 2px);
            top: calc(var(--header-top) * 0.25 + 1rem);
            width: calc(var(--button-action) - 2px);
            border-radius: 0px;
            padding: 0.5em;
            grid-column: 3;
            right: 10vw;
            display: flex;
            z-index: 9;
            align-items: center;
            justify-content: center;
            text-align: center;
            line-height: 1.1;
            font-size: 0.8em;
            font-weight: 400;
            text-transform: uppercase;
            transition: all 0.6s;
            border-radius: 50%;

            a {
                color:white;
            }
        }
            */
        .titol-pagina {
            grid-column: 2;
        }
    }

    .paragraph {
        opacity:1;
        transform: translateY(0);
    }
    p {
            font-size: calc(var(--font-m));
            line-height: 1.6;
    }
    .paragraph--type--paragraph-destacat .contingut-destacat>.row>.informacio {
        padding-left:0;
        
        h2 {
            margin-top: 4.1rem;
            font-weight:700;
            text-transform:none;
            color:var(--back-corpo);
        }
    }
    .paragraph{
        padding-top:0!important;
        background:transparent;
            font-size: calc(var(--font-m));

    }
}


// HOME 

.path-frontpage {

    #page-aglaia-wrapper {
        display: block;
        grid-template-columns: repeat(14, 1fr);
        gap: var(--gap);
        padding-left: calc(var(--page-horizontal-margin) * 0.5);
        padding-right: calc(var(--page-horizontal-margin) * 0.5);

        @media(min-width:1024px){
            display:grid;
            padding-left:0;
            padding-right:0;
        }
    }

    #vue {
        grid-column: 2 / span 5;
        background: transparent;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding:0;
        padding-top: 6rem;
        padding-bottom:2em;

        @media(min-width:1024px){
            padding: 10rem 0 8rem;
        }

        >header {
            position: static;
            background: transparent;

            padding:0;

            @media(min-width:1024px){
                padding: 0.7rem;
                padding-right: 1em;
            }

            #main-title {
                border: 0;
                font-size: calc(var(--font-l)* 1);

                @media(min-width:1024px){
                     font-size: calc(var(--font-xl)* 1);
                }

                span {
                    
                    display: block;
                    letter-spacing: -0.06em;

                    font-size: calc(var(--font-m-et-plus)* 1);

                    @media(min-width:1024px){
                        font-size: calc(var(--font-l)* 1);
                    }

                }

            }

            .titol-pagina,
            .llista-button {
                display: none;


            }
        }


    }

    .highlighted {
        display: none;
    }

    main {
        grid-column: 7 / span 6;
        padding: 0;
        background: transparent;
        grid-row: 1;

        .field-paragraph-intro-text {
            text-align: left;
        }
    }

    .paragraph {
        opacity: 1;
    }

    .paragraph--type--paragraph-intro {
    
        padding: 0;

        @media(min-width:1024px){
            padding: 10rem 0 8rem;
        }

        p {
            margin:0;

            @media(min-width:1024px){
               margin-top:1em;
               margin-bottom: 2.5rem;
            }
        }
        

        .field-paragraph-intro-titol {
            text-align: left;
            margin-bottom: 1em;
            margin-top: 0;
            padding: 0;
            font-size: calc(var(--font-m)*1.2);
            font-weight: 700;

            @media(min-width:1024px){

            margin-bottom: 0;
            }
        }
        .field-paragraph-intro-text p{
            font-size: calc(var(--font-m));
            line-height:1.6;
        }

    }
}

//AGLAIA

body .aglaia-go-top {

    width: var(--button-action);
    height: var(--button-action);
    right: 4vw;
    padding: 0;


    &:before {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        content: "";
        background-image: url(/cartaarqueologica/web/modules/custom/mapa_module/img/arrow-up.svg);
        width: 33%;
        height: 33%;
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
        display: block;
        z-index: 2;
        transition: all 0.6s;
    }

    &:hover {
        &:before {
            transform: translate(-50%, -70%);
        }
    }

    .go-top {
        width: 100%;
        height: 100%;

        position: relative;


        .b,
        .f,
        .c {
            width: 100%;
            height: 100%;
        }

        .c {
            fill: var(--back-corpo) !important;
            stroke: var(--back-corpo) !important;

        }

        .d {
            display: none;
        }
    }

    .sr-only {
        display: none;
    }
}



//TIMELINE

.timeline {
    position: fixed;
    z-index: 10;
    bottom: 10vh;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height:0;
    opacity: 0.6;
    transition: opacity 0.6s;
    display:none;

    @media(min-width: 1024px){
       display:block;
    }

    &:hover {
        opacity: 1;
    }

    #grafo {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(10, 1fr);
        margin: 0;
        height: 0px !important;

        &:after {
            width: 100%;
            height: 1px;
            background: var(--back-corpo);
            content: "";
            display: bloc;
            position: absolute;
            top: 50%;
            left: 0;
        }

        li {
            position: relative;
            margin: 0;
            padding: 0;
            pointer-events:none;


            span {
                opacity: 0.4;
                position: absolute;
                //    background-color:var(--back-corpo);
                border-radius: 50%;
                transition: background-color 0.6s;
                background: var(--back-corpo);//radial-gradient(circle, var(--back-corpo) 30%, transparent 90%);

            }


        }
    }
}


// drupal

.toolbar-lining,
.toolbar-bar {
    width: 100%;
}

#block-aglaia-subtheme-primary-local-tasks {
    background: white !important;
    position: fixed;
    top: 40px;
    right: 0;
    height: 3rem;
    width: 3rem;
    z-index: 20;
    overflow: hidden;
    padding-top: 3rem;

    &:hover {
        width: 15rem;
        height: 100%;
    }
}

.toolbar-tray-open.toolbar-vertical.toolbar-fixed {
    margin: 0 !important;
}

.toolbar-oriented .toolbar-tray-vertical.is-active {
    width: 2.8rem;
    transition: all 0.6s;

    &:hover {
        width: 15rem;
    }
}


.loaded {

    .llista-button {
        color: white;
    }

    #vue,
    #no-vue {
        >header {

            .resultat-cerca,
            .llista-button {
                opacity: 1;

            }
        }
    }

    .path-stories,
    .page-node-type-relat {

        #vue,
        #no-vue {
            >header {

                .resultat-cerca,
                .llista-button {
                    display: none;
                }
            }
        }
    }

}

.coeli-debug-box {
    position: fixed;
    z-index: 10000;
    background: white;
    padding: 3rem;
    bottom: 0px;
    top: auto;
}



.paragraph {
    opacity: 1;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;

    @media(min-width: 1024px) {
        opacity: 0;
       
    }
}

.paragraph.visible {
    opacity: 1;
    transform: translateY(0);
}

//- LOADS -----------------------

.page-node-type-relat {

    main {
        background: #222;

        header {
            figure img {
                opacity: 0;
                transition: opacity 1s;
            }

            h1 {
                opacity: 0;
                transition: opacity 1s 0.5s;
            }
        }
    }

    &.visible {
        opacity: 1;

        main header {
            figure img {
                opacity: 1;
            }

            h1 {
                opacity: 1;
            }
        }
    }
}

.content-map-relat {
    opacity: 0;
    transition: opacity 1.8s;

    &.visible {
        opacity: 1;


        &:nth-child(1) {
            transition:
                transform 0.6s 0.3s,
                filter 0.6s 0.3s,
                opacity 1s 0s;

        }

        &:nth-child(2) {
            transition:
                transform 0.6s 0.3s,
                filter 0.6s 0.3s,
                opacity 1s 0.3s;
        }

        &:nth-child(3) {
            transition:
                transform 0.6s 0.3s,
                filter 0.6s 0.3s,
                opacity 1s 0.6s;
        }

        &:nth-child(4) {
            transition:
                transform 0.6s 0.3s,
                filter 0.6s 0.3s,
                opacity 1s 0.9s;
        }

        &:nth-child(5) {
            transition:
                transform 0.6s 0.3s,
                filter 0.6s 0.3s,
                opacity 1s 1.2s;
        }

        &:nth-child(6) {
            transition:
                transform 0.6s 0.3s,
                filter 0.6s 0.3s,
                opacity 1s 1.5s;
        }

        &:nth-child(7) {
            transition:
                transform 0.6s 0.3s,
                filter 0.6s 0.3s,
                opacity 1s 3s;
        }
    }

}

#main-content.unload {
    >* {
        opacity: 0;
        transition: opacity 0.4s ease;
    }
}

#menu-nav .links a:hover  {
    color: var(--back-corpo)!important;
}