build:
  assessment:
    validate_codebase:
      phplint:
      csslint:
        halt-on-fail: false
      eslint:
        halt-on-fail: false
      phpcs:
        sniff-all-files: false
        halt-on-fail: false
    testing:
      run_tests.phpunit:
        types: 'PHPUnit-Unit'
        testgroups: '--all'
        suppress-deprecations: true
        halt-on-fail: false
      run_tests.kernel:
        types: 'PHPUnit-Kernel'
        testgroups: '--all'
        suppress-deprecations: true
        halt-on-fail: false
      run_tests.simpletest:
         types: 'Simpletest'
         testgroups: '--all'
         suppress-deprecations: true
         halt-on-fail: false
      run_tests.build:
        types: 'PHPUnit-Build'
        testgroups: '--all'
        suppress-deprecations: true
        halt-on-fail: false
      run_tests.functional:
        types: 'PHPUnit-Functional'
        testgroups: '--all'
        suppress-deprecations: true
        halt-on-fail: false
      run_tests.javascript:
        concurrency: 15
        types: 'PHPUnit-FunctionalJavascript'
        testgroups: '--all'
        suppress-deprecations: true
        halt-on-fail: false
      nightwatchjs: {}
