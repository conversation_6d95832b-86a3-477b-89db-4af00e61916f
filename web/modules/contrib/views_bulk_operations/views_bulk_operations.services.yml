services:
  _defaults:
    autowire: true

  views_bulk_operations.data:
    class: Drupal\views_bulk_operations\Service\ViewsBulkOperationsViewData
  Drupal\views_bulk_operations\Service\ViewsBulkOperationsViewDataInterface: '@views_bulk_operations.data'

  views_bulk_operations.processor:
    class: Drupal\views_bulk_operations\Service\ViewsBulkOperationsActionProcessor
  Drupal\views_bulk_operations\Service\ViewsBulkOperationsActionProcessorInterface: '@views_bulk_operations.processor'

  plugin.manager.views_bulk_operations_action:
    class: Drupal\views_bulk_operations\Service\ViewsBulkOperationsActionManager
  Drupal\views_bulk_operations\Service\ViewsBulkOperationsActionManager: '@plugin.manager.views_bulk_operations_action'

  views_bulk_operations.access:
    class: Drupal\views_bulk_operations\Access\ViewsBulkOperationsAccess
    tags:
      - { name: access_check, applies_to: _views_bulk_operation_access }

  views_bulk_operations.view_data_provider:
    class: Drupal\views_bulk_operations\EventSubscriber\ViewsBulkOperationsEventSubscriber
    tags:
      - { name: event_subscriber }
