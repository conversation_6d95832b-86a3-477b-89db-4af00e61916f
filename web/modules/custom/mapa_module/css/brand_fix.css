/* Fix for brand header visibility with map */

/* Prevent body scrolling when brand header is present */
body.amb-brand {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100vh !important;
}

/* When #brand header is present, body gets .amb-brand class */
body.amb-brand .content-map {
  /* Keep map at top but ensure header is above it */
  top: 0 !important;
  /* Full height */
  height: 100vh !important;
}

/* Also adjust the map element inside - keep it at full size */
body.amb-brand .content-map #map {
  top: 0 !important;
  height: 100vh !important;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 411px) {
  body.amb-brand .content-map {
    top: 0 !important;
    height: 100vh !important;
  }

  body.amb-brand .content-map #map {
    top: 0 !important;
    height: 100vh !important;
  }
}

/* Additional fix for any map_fitxa elements that might exist */
body.amb-brand .content-map #map_fitxa {
  top: 0 !important;
  height: 100vh !important;
}

@media screen and (max-width: 411px) {
  body.amb-brand .content-map #map_fitxa {
    top: 0 !important;
    height: 100vh !important;
  }
}

/* Ensure brand header stays on top and is not scrollable */
body.amb-brand #brand {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  height: 4.6rem !important;
  background: white !important;
  pointer-events: auto !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  border-bottom: 1px solid #ddd !important;
}

@media screen and (max-width: 411px) {
  body.amb-brand #brand {
    height: 3.7rem !important;
  }
}

/* Ensure Vue header doesn't overlap with brand header */
body.amb-brand #vue > header,
body.amb-brand #no-vue > header {
  top: calc(4.6rem + 1vh) !important;
}

@media screen and (max-width: 411px) {
  body.amb-brand #vue > header,
  body.amb-brand #no-vue > header {
    top: calc(3.7rem + 1vh) !important;
  }
}
