/* Fix for brand header visibility with map */

/* When #brand header is present, body gets .amb-brand class */
body.amb-brand .content-map {
  /* Adjust top position to be below the brand header */
  top: 4.6rem !important;
  /* Adjust height to account for the header */
  height: calc(100vh - 4.6rem) !important;
}

/* Also adjust the map element inside */
body.amb-brand .content-map #map {
  top: 4.6rem !important;
  height: calc(100vh - 4.6rem) !important;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 411px) {
  body.amb-brand .content-map {
    top: 3.7rem !important;
    height: calc(100vh - 3.7rem) !important;
  }

  body.amb-brand .content-map #map {
    top: 3.7rem !important;
    height: calc(100vh - 3.7rem) !important;
  }
}

/* Additional fix for any map_fitxa elements that might exist */
body.amb-brand .content-map #map_fitxa {
  top: 4.6rem !important;
  height: calc(100vh - 4.6rem) !important;
}

@media screen and (max-width: 411px) {
  body.amb-brand .content-map #map_fitxa {
    top: 3.7rem !important;
    height: calc(100vh - 3.7rem) !important;
  }
}

/* Ensure Vue header doesn't overlap with brand header */
body.amb-brand #vue > header,
body.amb-brand #no-vue > header {
  top: calc(4.6rem + 1vh) !important;
}

@media screen and (max-width: 411px) {
  body.amb-brand #vue > header,
  body.amb-brand #no-vue > header {
    top: calc(3.7rem + 1vh) !important;
  }
}
