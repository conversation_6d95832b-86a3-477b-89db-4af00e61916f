mapa:
  version: 1.x
  css:
    theme:
      css/style.css: {}
      css/mapa_styles.css: {}
      css/unitats_layers.css: {}
      css/map_styles.css: {}
      css/brand_fix.css: {}
     # '//unpkg.com/leaflet@1.7.1/dist/leaflet.css': { type: external, minified: true }
      '//betaserver.icgc.cat/vectortiles/css/mapbox-gl-dev.css?v=123123124': { type: external, minified: true }
      '//api.mapbox.com/mapbox-gl-js/v3.9.4/mapbox-gl.css': { type: external, minified: true }
  js:
    '//betaserver.icgc.cat/vectortiles/js/mapbox-gl-dev.js?v=123123124': {type: external}
    '//betaserver.icgc.cat/pintamaps/js/vendors/jquery-2.1.4.min.js': {type: external}
   #  '//unpkg.com/leaflet@1.7.1/dist/leaflet.js': {type: external}
    '//api.mapbox.com/mapbox-gl-js/v3.9.4/mapbox-gl.js': {type: external}
    # Legacy files - IMPORTANT: toponimia.js must load before map.js
    map/map_config.js: {}
    map/map_layers_config.js: {}
    map/toponimia.js: {}
    map/map.js: {}
    map/map_markers.js: {}
    map/map_crono.js: {}
    map/map_colors.js: {}
    map/map_interface.js: {}
    map/wNumb.min.js: {}
    map/nouislider.min.js: {}
    # Vue component (loads last)
    js/vue.js: {}
  dependencies:
      - core/jquery
      - core/drupal
      - core/drupalSettings
      - mapa_module/coeli_core
crono:
  version: 1.x
  css:

    theme:

      css/mapa_crono.css: {}
  js:

    map/map_crono.js: {}
    map/wNumb.min.js: {}
    map/nouislider.min.js: {}

  dependencies:
      - core/jquery
      - core/drupal
      - core/drupalSettings
      - mapa_module/coeli_core

custom:
  version: 1.x
  css:
    theme:
      css/custom.css: {}
  js:
    js/custom.js: {}
  dependencies:
    - mapa_module/coeli_core
coeli_core:
  version: 1.x
  js:
    coeli_core/assets/build/js/vue_obj.js: {}
    coeli_core/assets/build/js/filtres.js: {}
    coeli_core/assets/build/js/front.js: {}
  css:
    theme:
      coeli_core/assets/build/css/search.css: {}
  dependencies:
    - mapa_module/vue
    - mapa_module/vuex
    - mapa_module/axios



vue:
  js:
    //unpkg.com/vue@3.2.31/dist/vue.global.prod.js: { type: external, minified: true }

vue-router:
  version: 3.1.3
  js:
    //cdnjs.cloudflare.com/ajax/libs/vue-router/3.1.3/vue-router.min.js: { type: external, minified: true }
  dependencies:
    - mapa_module/vue

vuex:
  version: 3.1.2
  js:
    //cdnjs.cloudflare.com/ajax/libs/vuex/3.1.2/vuex.min.js: { type: external, minified: true }
  dependencies:
    - mapa_module/vue

axios:
  version: 0.19.2
  js:
    //cdnjs.cloudflare.com/ajax/libs/axios/0.19.2/axios.min.js: { type: external, minified: true }

