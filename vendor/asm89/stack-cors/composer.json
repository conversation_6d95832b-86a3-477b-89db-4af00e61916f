{"name": "asm89/stack-cors", "description": "Cross-origin resource sharing library and stack middleware", "keywords": ["stack", "cors"], "homepage": "https://github.com/asm89/stack-cors", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0", "symfony/http-foundation": "^5.3|^6|^7", "symfony/http-kernel": "^5.3|^6|^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "autoload-dev": {"psr-4": {"Asm89\\Stack\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "check-style": "phpcs -p --standard=PSR12 --exclude=Generic.Files.LineLength --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src", "fix-style": "phpcbf -p --standard=PSR12 --exclude=Generic.Files.LineLength --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src"}, "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "minimum-stability": "beta", "prefer-stable": true}