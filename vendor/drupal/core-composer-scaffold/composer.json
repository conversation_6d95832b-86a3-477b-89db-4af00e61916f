{"name": "drupal/core-composer-scaffold", "description": "A flexible Composer project scaffold builder.", "type": "composer-plugin", "keywords": ["drupal"], "homepage": "https://www.drupal.org/project/drupal", "license": "GPL-2.0-or-later", "require": {"composer-plugin-api": "^2", "php": ">=7.3.0"}, "conflict": {"drupal-composer/drupal-scaffold": "*"}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\Scaffold\\": ""}}, "extra": {"class": "Drupal\\Composer\\Plugin\\Scaffold\\Plugin", "branch-alias": {"dev-master": "1.0.x-dev"}}, "config": {"sort-packages": true}, "require-dev": {"composer/composer": "^1.8@stable"}}