<?php return array(
    'root' => array(
        'name' => 'ajbcn/aglaia-skeleton',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '8597b59f9df67c3c7d88b43ee1f61450f1b17eb8',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'ajbcn/aglaia' => array(
            'pretty_version' => '10.0.24',
            'version' => '10.0.24.0',
            'reference' => '72bf3c598d953744f2d64ca57a3b8844271ea62d',
            'type' => 'ajbcn-drupal-profile',
            'install_path' => __DIR__ . '/../../web/profiles/ajbcn/aglaia',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/aglaia-skeleton' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '8597b59f9df67c3c7d88b43ee1f61450f1b17eb8',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/aglaia_menu' => array(
            'pretty_version' => '10.0.6',
            'version' => '10.0.6.0',
            'reference' => '91e6650f616c40f71c3d023b0f57db0f3ca10c26',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/aglaia_menu',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/aglaia_overrides' => array(
            'pretty_version' => '10.0.4',
            'version' => '10.0.4.0',
            'reference' => '2f01ee4a491d5ba09dc27afb3df0157ea424212c',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/aglaia_overrides',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/aglaia_theme' => array(
            'pretty_version' => '10.0.7',
            'version' => '10.0.7.0',
            'reference' => '44099528006470145f15bef0bd84a1b9afb5f9b2',
            'type' => 'ajbcn-drupal-theme',
            'install_path' => __DIR__ . '/../../web/themes/ajbcn/aglaia_theme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_accessibilitat' => array(
            'pretty_version' => '10.0.0',
            'version' => '10.0.0.0',
            'reference' => '24ca8cd857f2fcbabe3789a1df1bd0fde528ae55',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_accessibilitat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_avislegal' => array(
            'pretty_version' => '10.0.4',
            'version' => '10.0.4.0',
            'reference' => '36afa7b190baca0fa16b54d760bdbde3a755459a',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_avislegal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_avisos' => array(
            'pretty_version' => '10.0.1',
            'version' => '10.0.1.0',
            'reference' => 'cb1561bc76bbf103c41d67cadada07c7f5a8de50',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_avisos',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_banners' => array(
            'pretty_version' => '10.0.8',
            'version' => '10.0.8.0',
            'reference' => '394fa2e03e7eeb0c1bcc1bc5a4015b0c79e27013',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_banners',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_embed_video' => array(
            'pretty_version' => '10.0.4',
            'version' => '10.0.4.0',
            'reference' => '92a6c4537eb0cbc8245548ef730fb92650205a54',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_embed_video',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_infofile' => array(
            'pretty_version' => '10.0.1',
            'version' => '10.0.1.0',
            'reference' => 'ba743f5eb58c0e839c4d96d62b098384a439a3f3',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_infofile',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_media_link' => array(
            'pretty_version' => '10.0.2',
            'version' => '10.0.2.0',
            'reference' => '1c235ac81ee2bc5dafa8c6e306259f0d4cb7634f',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_media_link',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_social_share' => array(
            'pretty_version' => '10.0.5',
            'version' => '10.0.5.0',
            'reference' => '6048a96753031077c6d3bfcc7f8edec169e532a1',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_social_share',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/ajuntament_weight' => array(
            'pretty_version' => '10.0.1',
            'version' => '10.0.1.0',
            'reference' => 'd6e21e7d3f5087a4a71ea9f9f687c6621f14f376',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/ajuntament_weight',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/bcn_barra' => array(
            'pretty_version' => '9.0.7',
            'version' => '9.0.7.0',
            'reference' => '13ab787b2d19b95da223427992403c839636f7a1',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/bcn_barra',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/bcn_cms_stats' => array(
            'pretty_version' => '9.0.4',
            'version' => '9.0.4.0',
            'reference' => 'f73638753737792c0c8b146ee52e8245fcf22e7b',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/bcn_cms_stats',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ajbcn/bcn_redirect_home' => array(
            'pretty_version' => '9.1.11',
            'version' => '9.1.11.0',
            'reference' => 'a683371c33bedd48e22dcba8b4d89e51942ebe82',
            'type' => 'ajbcn-drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/ajbcn/bcn_redirect_home',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'asm89/stack-cors' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'reference' => 'acf3142e6c5eafa378dc8ef3c069ab4558993f70',
            'type' => 'library',
            'install_path' => __DIR__ . '/../asm89/stack-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/fancybox' => array(
            'pretty_version' => 'v3.5.7',
            'version' => '3.5.7.0',
            'reference' => 'b507d317a95b70a0b1be7209d528c4dba3ee0dd0',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../../web/libraries/fancybox',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/jquery' => array(
            'pretty_version' => '3.7.1',
            'version' => '3.7.1.0',
            'reference' => 'fde1f76e2799dd877c176abde0ec836553246991',
            'type' => 'bower-asset',
            'install_path' => __DIR__ . '/../../web/libraries/jquery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'chi-teck/drupal-code-generator' => array(
            'pretty_version' => '3.6.1',
            'version' => '3.6.1.0',
            'reference' => '2dbd8d231945681a398862a3282ade3cf0ea23ab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../chi-teck/drupal-code-generator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/installers' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'reference' => '12fb2dfe5e16183de69e784a7b84046c43d97e8e',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/annotated-command' => array(
            'pretty_version' => '4.10.1',
            'version' => '4.10.1.0',
            'reference' => '362310b13ececa9f6f0a4a880811fa08fecc348b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/annotated-command',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/config' => array(
            'pretty_version' => '2.1.2',
            'version' => '2.1.2.0',
            'reference' => '597f8d7fbeef801736250ec10c3e190569b1b0ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/filter-via-dot-access-data' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/filter-via-dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/log' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'c27a3beb36137c141ccbce0d89f64befb243c015',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/output-formatters' => array(
            'pretty_version' => '4.6.0',
            'version' => '4.6.0.0',
            'reference' => '5fd5656718d7068a02d046f418a7ba873d5abbfe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/output-formatters',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/robo' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => '55a272370940607649e5c46eb173c5c54f7c166d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/robo',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/self-update' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '972a1016761c9b63314e040836a12795dff6953a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/self-update',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/site-alias' => array(
            'pretty_version' => '4.1.1',
            'version' => '4.1.1.0',
            'reference' => 'aff6189aae17da813d23249cb2fc0fff33f26d40',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/site-alias',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/site-process' => array(
            'pretty_version' => '5.4.2',
            'version' => '5.4.2.0',
            'reference' => 'e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/site-process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cweagans/composer-patches' => array(
            'pretty_version' => '1.7.3',
            'version' => '1.7.3.0',
            'reference' => 'e190d4466fe2b103a55467dfa83fc2fecfcaf2db',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../cweagans/composer-patches',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/annotations' => array(
            'pretty_version' => '1.14.4',
            'version' => '1.14.4.0',
            'reference' => '253dca476f70808a5aeed3a47cc2cc88c5cab915',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/annotations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/admin_toolbar' => array(
            'pretty_version' => '3.6.1',
            'version' => '3.6.1.0',
            'reference' => '3.6.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/admin_toolbar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/allowed_formats' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '3.0.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/allowed_formats',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/block_content_permissions' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '8.x-1.11',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/block_content_permissions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/color_field' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => '3.0.2',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/color_field',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/colorbox' => array(
            'pretty_version' => '2.1.4',
            'version' => '2.1.4.0',
            'reference' => '2.1.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/colorbox',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/config_ignore' => array(
            'pretty_version' => '3.3.0',
            'version' => '3.3.0.0',
            'reference' => '8.x-3.3',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/config_ignore',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core' => array(
            'pretty_version' => '10.5.1',
            'version' => '10.5.1.0',
            'reference' => '551442fec1db69cf6eedb1601a348d8a6268060f',
            'type' => 'drupal-core',
            'install_path' => __DIR__ . '/../../web/core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-annotation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-assertion' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-class-finder' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-composer-scaffold' => array(
            'pretty_version' => '10.5.1',
            'version' => '10.5.1.0',
            'reference' => 'db17b59620ce1c142a34dc017d9e696ce4771e55',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../drupal/core-composer-scaffold',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-datetime' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-dependency-injection' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-diff' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-discovery' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-event-dispatcher' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-file-cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-file-security' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-front-matter' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-gettext' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-graph' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-http-foundation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-php-storage' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-plugin' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-project-message' => array(
            'pretty_version' => '10.5.1',
            'version' => '10.5.1.0',
            'reference' => 'd1da83722735cb0f7ccabf9fef7b5607b442c3a8',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../drupal/core-project-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-proxy-builder' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-recommended' => array(
            'pretty_version' => '10.5.1',
            'version' => '10.5.1.0',
            'reference' => '60b76ba11c2ae9088283a1e6963b929ca976f4fc',
            'type' => 'metapackage',
            'install_path' => null,
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-render' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-serialization' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-transliteration' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-utility' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/core-version' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.5.1',
            ),
        ),
        'drupal/crop' => array(
            'pretty_version' => '2.4.0',
            'version' => '*******',
            'reference' => '8.x-2.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/crop',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/ctools' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => '4.1.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/ctools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/draggableviews' => array(
            'pretty_version' => '2.1.4',
            'version' => '2.1.4.0',
            'reference' => '2.1.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/draggableviews',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/dropzonejs' => array(
            'pretty_version' => '2.11.0',
            'version' => '2.11.0.0',
            'reference' => '8.x-2.11',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/dropzonejs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/dropzonejs_eb_widget' => array(
            'pretty_version' => '2.11.0',
            'version' => '2.11.0.0',
            'reference' => null,
            'type' => 'metapackage',
            'install_path' => null,
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/ds' => array(
            'pretty_version' => '3.30.0',
            'version' => '3.30.0.0',
            'reference' => '8.x-3.30',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/ds',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/editor_advanced_link' => array(
            'pretty_version' => '2.3.1',
            'version' => '2.3.1.0',
            'reference' => '2.3.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/editor_advanced_link',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/embed' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'reference' => '8.x-1.10',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/embed',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/entity_browser' => array(
            'pretty_version' => '2.13.0',
            'version' => '2.13.0.0',
            'reference' => '8.x-2.13',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/entity_browser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/entity_clone' => array(
            'pretty_version' => 'dev-1.x',
            'version' => 'dev-1.x',
            'reference' => '36a2dae4c7c08bbd09a1017d102ef2360eb6e460',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/entity_clone',
            'aliases' => array(
                0 => '1.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'drupal/entity_embed' => array(
            'pretty_version' => '1.7.0',
            'version' => '1.7.0.0',
            'reference' => '8.x-1.7',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/entity_embed',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/entity_reference_revisions' => array(
            'pretty_version' => '1.12.0',
            'version' => '1.12.0.0',
            'reference' => '8.x-1.12',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/entity_reference_revisions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/field_group' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'reference' => '8.x-3.6',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/field_group',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/file_browser' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => '8.x-1.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/file_browser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/file_replace' => array(
            'pretty_version' => '1.5.0',
            'version' => '*******',
            'reference' => '8.x-1.5',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/file_replace',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/flippy' => array(
            'pretty_version' => '2.0.0-beta1',
            'version' => '*******-beta1',
            'reference' => '2.0.0-beta1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/flippy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/fontawesome' => array(
            'pretty_version' => '2.26.0',
            'version' => '********',
            'reference' => '8.x-2.26',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/fontawesome',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/image_widget_crop' => array(
            'pretty_version' => '2.4.0',
            'version' => '*******',
            'reference' => '8.x-2.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/image_widget_crop',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/inline_entity_form' => array(
            'pretty_version' => '1.0.0-rc17',
            'version' => '*******-RC17',
            'reference' => '8.x-1.0-rc17',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/inline_entity_form',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/link_attributes' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '2.1.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/link_attributes',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/menu_admin_per_menu' => array(
            'pretty_version' => '1.7.0',
            'version' => '1.7.0.0',
            'reference' => '8.x-1.7',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/menu_admin_per_menu',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/menu_breadcrumb' => array(
            'pretty_version' => '2.0.0',
            'version' => '*******',
            'reference' => '2.0.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/menu_breadcrumb',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/menu_multilingual' => array(
            'pretty_version' => '1.0.0',
            'version' => '*******',
            'reference' => '8.x-1.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/menu_multilingual',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/metatag' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '2.1.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/metatag',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/paragraphs' => array(
            'pretty_version' => '1.19.0',
            'version' => '1.19.0.0',
            'reference' => '8.x-1.19',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/paragraphs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/paragraphs_asymmetric_translation_widgets' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => '8.x-1.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/paragraphs_asymmetric_translation_widgets',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/pathauto' => array(
            'pretty_version' => '1.13.0',
            'version' => '1.13.0.0',
            'reference' => '8.x-1.13',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/pathauto',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/redirect' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '8.x-1.11',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/redirect',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/redis' => array(
            'pretty_version' => '1.9.0',
            'version' => '1.9.0.0',
            'reference' => '8.x-1.9',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/redis',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/rename_admin_paths' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '3.0.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/rename_admin_paths',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/simple_sitemap' => array(
            'pretty_version' => '4.2.2',
            'version' => '4.2.2.0',
            'reference' => '4.2.2',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/simple_sitemap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/sitemap' => array(
            'pretty_version' => '2.0.0',
            'version' => '*******',
            'reference' => '8.x-2.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/sitemap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/svg_image' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '3.2.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/svg_image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/svg_image_field' => array(
            'pretty_version' => '2.3.4',
            'version' => '2.3.4.0',
            'reference' => '2.3.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/svg_image_field',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/token' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => '8.x-1.15',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/token',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/tvi' => array(
            'pretty_version' => 'dev-2.0.x',
            'version' => 'dev-2.0.x',
            'reference' => 'd5349cf21299dca972bf95147609c7e4fbe9612b',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/tvi',
            'aliases' => array(
                0 => '2.0.x-dev',
            ),
            'dev_requirement' => false,
        ),
        'drupal/twig_tweak' => array(
            'pretty_version' => '3.4.1',
            'version' => '3.4.1.0',
            'reference' => '3.4.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/twig_tweak',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/twig_vardumper' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '3.2.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/twig_vardumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/unpublished_node_permissions' => array(
            'pretty_version' => '1.6.0',
            'version' => '1.6.0.0',
            'reference' => '8.x-1.6',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/unpublished_node_permissions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/views_bulk_operations' => array(
            'pretty_version' => '4.3.4',
            'version' => '4.3.4.0',
            'reference' => '4.3.4',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/views_bulk_operations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/views_infinite_scroll' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '2.0.3',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/views_infinite_scroll',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drush/drush' => array(
            'pretty_version' => '12.5.3',
            'version' => '12.5.3.0',
            'reference' => '7fe0a492d5126c457c5fb184c4668a132b0aaac6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../drush/drush',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'enshrined/svg-sanitize' => array(
            'pretty_version' => '0.21.0',
            'version' => '0.21.0.0',
            'reference' => '5e477468fac5c5ce933dce53af3e8e4e58dcccc9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../enshrined/svg-sanitize',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'grasmash/expander' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'eea11b9afb0c32483b18b9009f4ca07b770e39f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../grasmash/expander',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'grasmash/yaml-cli' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '09a8860566958a1576cc54bbe910a03477e54971',
            'type' => 'library',
            'install_path' => __DIR__ . '/../grasmash/yaml-cli',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/container' => array(
            'pretty_version' => '4.2.5',
            'version' => '4.2.5.0',
            'reference' => 'd3cebb0ff4685ff61c749e54b27db49319e2ec00',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mck89/peast' => array(
            'pretty_version' => 'v1.17.2',
            'version' => '1.17.2.0',
            'reference' => '465810689c477fbba17f4f949b75e4d0bdab13ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mck89/peast',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'ae59794362fe85e051a58ad36b289443f57be7a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'npm-asset/accessible360--accessible-slick' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => null,
            'type' => 'npm-asset',
            'install_path' => __DIR__ . '/../../web/libraries/accessible360--accessible-slick',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'npm-asset/blazy' => array(
            'pretty_version' => '1.8.2',
            'version' => '1.8.2.0',
            'reference' => null,
            'type' => 'npm-asset',
            'install_path' => __DIR__ . '/../../web/libraries/blazy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'npm-asset/cropper' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => null,
            'type' => 'npm-asset',
            'install_path' => __DIR__ . '/../../web/libraries/cropper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'npm-asset/cropperjs' => array(
            'pretty_version' => '1.6.2',
            'version' => '1.6.2.0',
            'reference' => null,
            'type' => 'npm-asset',
            'install_path' => __DIR__ . '/../../web/libraries/cropperjs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'oomphinc/composer-installers-extender' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'cbf4b6f9a24153b785d09eee755b995ba87bd5f9',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../oomphinc/composer-installers-extender',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'orno/di' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '~2.0',
            ),
        ),
        'pear/archive_tar' => array(
            'pretty_version' => '1.5.0',
            'version' => '*******',
            'reference' => 'b439c859564f5cbb0f64ad6002d0afe84a889602',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/archive_tar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/console_getopt' => array(
            'pretty_version' => 'v1.4.3',
            'version' => '1.4.3.0',
            'reference' => 'a41f8d3e668987609178c7c4a9fe48fecac53fa0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/console_getopt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear-core-minimal' => array(
            'pretty_version' => 'v1.10.16',
            'version' => '1.10.16.0',
            'reference' => 'c0f51b45f50683bf5bbf558036854ebc9b54d033',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/pear-core-minimal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear_exception' => array(
            'pretty_version' => 'v1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'b14fbe2ddb0b9f94f5b24cf08783d599f776fff0',
            'type' => 'class',
            'install_path' => __DIR__ . '/../pear/pear_exception',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phootwork/collection' => array(
            'pretty_version' => 'v3.2.3',
            'version' => '3.2.3.0',
            'reference' => '46dde20420fba17766c89200bc3ff91d3e58eafa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phootwork/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phootwork/lang' => array(
            'pretty_version' => 'v3.2.3',
            'version' => '3.2.3.0',
            'reference' => '52ec8cce740ce1c424eef02f43b43d5ddfec7b5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phootwork/lang',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpowermove/docblock' => array(
            'pretty_version' => 'v4.0',
            'version' => '4.0.0.0',
            'reference' => 'a73f6e17b7d4e1b92ca5378c248c952c9fae7826',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpowermove/docblock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
                1 => '^1.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '*******',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '*******',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.9',
            'version' => '0.12.9.0',
            'reference' => '1b801844becfe648985372cb4b12ad6840245ace',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rsky/pear-core-min' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v1.10.16',
            ),
        ),
        'scssphp/scssphp' => array(
            'pretty_version' => 'v1.13.0',
            'version' => '1.13.0.0',
            'reference' => '63d1157457e5554edf00b0c1fabab4c1511d2520',
            'type' => 'library',
            'install_path' => __DIR__ . '/../scssphp/scssphp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '9056771b8eca08d026cd3280deeec3cfd99c4d93',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dependency-injection' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '0d9f24f3de0a83573fce5c9ed025d6306c6e166b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dependency-injection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => 'b088e0b175c30b4e06d8085200fa465b586f44fa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '7642f5e970b672283b7823222ae8ef8bbc160b9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '4856c9cf585d5a0313d8d35afd681a526f038dd3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '452d19f945ee41345fd8a50c18b60783546b7bd3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '2bb2cba685aabd859f22cf6946554e8e7f3c329a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => 'a480322ddf8e54de262c9bca31fdcbe26b553de5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'fec8aa5231f3904754955fad33c2db50594d22d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '48becf00c920479ca2e910c22a5a39e5d47ca956',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.4.20',
            'version' => '6.4.20.0',
            'reference' => 'e2a61c16af36c9a07e5c9906498b73e091949a20',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/psr-http-message-bridge' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => 'c9cf83326a1074f83a738fc5320945abf7fb7fec',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/psr-http-message-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '1f5234e8457164a3a0038a4c0a4ba27876a9c670',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/serializer' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => 'b40a697a2bb2c3d841a1f9e34a8a9f50bf9d1d06',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/serializer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '73e2c6966a5aef1d4892873ed5322245295370c6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '4667ff3bd513750603a09c8dedbea942487fb07c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '6506760ab57e7cda5bde9cdaed736526162284bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => 'd55b1834cdbfcc31bc2cd7e095ba5ed9a88f6600',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => 'f28cf841f5654955c9f88ceaf4b9dc29571988a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '93e29e0deb5f1b2e360adfb389a20d25eb81a27b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.20.0',
            'version' => '3.20.0.0',
            'reference' => '3468920399451a384bef53cf7996965f7cd40183',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.2',
            'version' => '5.6.2.0',
            'reference' => '24ac4c74f91ee2c193fa1aaa5c249cb0822809af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webflo/drupal-finder' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'reference' => '73045060b0894c77962a10cff047f72872d8810c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webflo/drupal-finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/path-util' => array(
            'pretty_version' => '2.3.0',
            'version' => '2.3.0.0',
            'reference' => 'd939f7edc24c9a1bb9c0dee5cb05d8e859490725',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/path-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
